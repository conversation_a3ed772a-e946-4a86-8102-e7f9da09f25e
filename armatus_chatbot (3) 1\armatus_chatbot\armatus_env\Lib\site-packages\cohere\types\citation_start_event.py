# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from .chat_stream_event_type import ChatStreamEventType
from .citation_start_event_delta import CitationStartEventDelta


class CitationStartEvent(ChatStreamEventType):
    """
    A streamed event which signifies a citation has been created.
    """

    index: typing.Optional[int] = None
    delta: typing.Optional[CitationStartEventDelta] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
