# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .api_meta import ApiMeta
from .embed_by_type_response_embeddings import EmbedByTypeResponseEmbeddings
from .image import Image


class EmbedByTypeResponse(UncheckedBaseModel):
    id: str
    embeddings: EmbedByTypeResponseEmbeddings = pydantic.Field()
    """
    An object with different embedding types. The length of each embedding type array will be the same as the length of the original `texts` array.
    """

    texts: typing.Optional[typing.List[str]] = pydantic.Field(default=None)
    """
    The text entries for which embeddings were returned.
    """

    images: typing.Optional[typing.List[Image]] = pydantic.Field(default=None)
    """
    The image entries for which embeddings were returned.
    """

    meta: typing.Optional[ApiMeta] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
