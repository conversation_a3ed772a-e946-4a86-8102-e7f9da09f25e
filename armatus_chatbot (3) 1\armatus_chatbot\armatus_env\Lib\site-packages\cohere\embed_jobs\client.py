# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ..core.client_wrapper import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Sync<PERSON><PERSON>Wrapper
from ..core.request_options import RequestOptions
from ..types.create_embed_job_response import CreateEmbedJobResponse
from ..types.embed_input_type import EmbedInputType
from ..types.embed_job import EmbedJob
from ..types.embedding_type import EmbeddingType
from ..types.list_embed_job_response import ListEmbedJobResponse
from .raw_client import AsyncRawEmbedJobsClient, RawEmbedJobsClient
from .types.create_embed_job_request_truncate import CreateEmbedJobRequestTruncate

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class EmbedJobsClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawEmbedJobsClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawEmbedJobsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawEmbedJobsClient
        """
        return self._raw_client

    def list(self, *, request_options: typing.Optional[RequestOptions] = None) -> ListEmbedJobResponse:
        """
        The list embed job endpoint allows users to view all embed jobs history for that specific user.

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ListEmbedJobResponse
            OK

        Examples
        --------
        from cohere import Client

        client = Client(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )
        client.embed_jobs.list()
        """
        _response = self._raw_client.list(request_options=request_options)
        return _response.data

    def create(
        self,
        *,
        model: str,
        dataset_id: str,
        input_type: EmbedInputType,
        name: typing.Optional[str] = OMIT,
        embedding_types: typing.Optional[typing.Sequence[EmbeddingType]] = OMIT,
        truncate: typing.Optional[CreateEmbedJobRequestTruncate] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> CreateEmbedJobResponse:
        """
        This API launches an async Embed job for a [Dataset](https://docs.cohere.com/docs/datasets) of type `embed-input`. The result of a completed embed job is new Dataset of type `embed-output`, which contains the original text entries and the corresponding embeddings.

        Parameters
        ----------
        model : str
            ID of the embedding model.

            Available models and corresponding embedding dimensions:

            - `embed-english-v3.0` : 1024
            - `embed-multilingual-v3.0` : 1024
            - `embed-english-light-v3.0` : 384
            - `embed-multilingual-light-v3.0` : 384

        dataset_id : str
            ID of a [Dataset](https://docs.cohere.com/docs/datasets). The Dataset must be of type `embed-input` and must have a validation status `Validated`

        input_type : EmbedInputType

        name : typing.Optional[str]
            The name of the embed job.

        embedding_types : typing.Optional[typing.Sequence[EmbeddingType]]
            Specifies the types of embeddings you want to get back. Not required and default is None, which returns the Embed Floats response type. Can be one or more of the following types.

            * `"float"`: Use this when you want to get back the default float embeddings. Valid for all models.
            * `"int8"`: Use this when you want to get back signed int8 embeddings. Valid for v3 and newer model versions.
            * `"uint8"`: Use this when you want to get back unsigned int8 embeddings. Valid for v3 and newer model versions.
            * `"binary"`: Use this when you want to get back signed binary embeddings. Valid for v3 and newer model versions.
            * `"ubinary"`: Use this when you want to get back unsigned binary embeddings. Valid for v3 and newer model versions.

        truncate : typing.Optional[CreateEmbedJobRequestTruncate]
            One of `START|END` to specify how the API will handle inputs longer than the maximum token length.

            Passing `START` will discard the start of the input. `END` will discard the end of the input. In both cases, input is discarded until the remaining input is exactly the maximum input token length for the model.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        CreateEmbedJobResponse
            OK

        Examples
        --------
        from cohere import Client

        client = Client(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )
        client.embed_jobs.create(
            model="model",
            dataset_id="dataset_id",
            input_type="search_document",
        )
        """
        _response = self._raw_client.create(
            model=model,
            dataset_id=dataset_id,
            input_type=input_type,
            name=name,
            embedding_types=embedding_types,
            truncate=truncate,
            request_options=request_options,
        )
        return _response.data

    def get(self, id: str, *, request_options: typing.Optional[RequestOptions] = None) -> EmbedJob:
        """
        This API retrieves the details about an embed job started by the same user.

        Parameters
        ----------
        id : str
            The ID of the embed job to retrieve.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        EmbedJob
            OK

        Examples
        --------
        from cohere import Client

        client = Client(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )
        client.embed_jobs.get(
            id="id",
        )
        """
        _response = self._raw_client.get(id, request_options=request_options)
        return _response.data

    def cancel(self, id: str, *, request_options: typing.Optional[RequestOptions] = None) -> None:
        """
        This API allows users to cancel an active embed job. Once invoked, the embedding process will be terminated, and users will be charged for the embeddings processed up to the cancellation point. It's important to note that partial results will not be available to users after cancellation.

        Parameters
        ----------
        id : str
            The ID of the embed job to cancel.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        None

        Examples
        --------
        from cohere import Client

        client = Client(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )
        client.embed_jobs.cancel(
            id="id",
        )
        """
        _response = self._raw_client.cancel(id, request_options=request_options)
        return _response.data


class AsyncEmbedJobsClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawEmbedJobsClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawEmbedJobsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawEmbedJobsClient
        """
        return self._raw_client

    async def list(self, *, request_options: typing.Optional[RequestOptions] = None) -> ListEmbedJobResponse:
        """
        The list embed job endpoint allows users to view all embed jobs history for that specific user.

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ListEmbedJobResponse
            OK

        Examples
        --------
        import asyncio

        from cohere import AsyncClient

        client = AsyncClient(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.embed_jobs.list()


        asyncio.run(main())
        """
        _response = await self._raw_client.list(request_options=request_options)
        return _response.data

    async def create(
        self,
        *,
        model: str,
        dataset_id: str,
        input_type: EmbedInputType,
        name: typing.Optional[str] = OMIT,
        embedding_types: typing.Optional[typing.Sequence[EmbeddingType]] = OMIT,
        truncate: typing.Optional[CreateEmbedJobRequestTruncate] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> CreateEmbedJobResponse:
        """
        This API launches an async Embed job for a [Dataset](https://docs.cohere.com/docs/datasets) of type `embed-input`. The result of a completed embed job is new Dataset of type `embed-output`, which contains the original text entries and the corresponding embeddings.

        Parameters
        ----------
        model : str
            ID of the embedding model.

            Available models and corresponding embedding dimensions:

            - `embed-english-v3.0` : 1024
            - `embed-multilingual-v3.0` : 1024
            - `embed-english-light-v3.0` : 384
            - `embed-multilingual-light-v3.0` : 384

        dataset_id : str
            ID of a [Dataset](https://docs.cohere.com/docs/datasets). The Dataset must be of type `embed-input` and must have a validation status `Validated`

        input_type : EmbedInputType

        name : typing.Optional[str]
            The name of the embed job.

        embedding_types : typing.Optional[typing.Sequence[EmbeddingType]]
            Specifies the types of embeddings you want to get back. Not required and default is None, which returns the Embed Floats response type. Can be one or more of the following types.

            * `"float"`: Use this when you want to get back the default float embeddings. Valid for all models.
            * `"int8"`: Use this when you want to get back signed int8 embeddings. Valid for v3 and newer model versions.
            * `"uint8"`: Use this when you want to get back unsigned int8 embeddings. Valid for v3 and newer model versions.
            * `"binary"`: Use this when you want to get back signed binary embeddings. Valid for v3 and newer model versions.
            * `"ubinary"`: Use this when you want to get back unsigned binary embeddings. Valid for v3 and newer model versions.

        truncate : typing.Optional[CreateEmbedJobRequestTruncate]
            One of `START|END` to specify how the API will handle inputs longer than the maximum token length.

            Passing `START` will discard the start of the input. `END` will discard the end of the input. In both cases, input is discarded until the remaining input is exactly the maximum input token length for the model.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        CreateEmbedJobResponse
            OK

        Examples
        --------
        import asyncio

        from cohere import AsyncClient

        client = AsyncClient(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.embed_jobs.create(
                model="model",
                dataset_id="dataset_id",
                input_type="search_document",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.create(
            model=model,
            dataset_id=dataset_id,
            input_type=input_type,
            name=name,
            embedding_types=embedding_types,
            truncate=truncate,
            request_options=request_options,
        )
        return _response.data

    async def get(self, id: str, *, request_options: typing.Optional[RequestOptions] = None) -> EmbedJob:
        """
        This API retrieves the details about an embed job started by the same user.

        Parameters
        ----------
        id : str
            The ID of the embed job to retrieve.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        EmbedJob
            OK

        Examples
        --------
        import asyncio

        from cohere import AsyncClient

        client = AsyncClient(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.embed_jobs.get(
                id="id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.get(id, request_options=request_options)
        return _response.data

    async def cancel(self, id: str, *, request_options: typing.Optional[RequestOptions] = None) -> None:
        """
        This API allows users to cancel an active embed job. Once invoked, the embedding process will be terminated, and users will be charged for the embeddings processed up to the cancellation point. It's important to note that partial results will not be available to users after cancellation.

        Parameters
        ----------
        id : str
            The ID of the embed job to cancel.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        None

        Examples
        --------
        import asyncio

        from cohere import AsyncClient

        client = AsyncClient(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.embed_jobs.cancel(
                id="id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.cancel(id, request_options=request_options)
        return _response.data
