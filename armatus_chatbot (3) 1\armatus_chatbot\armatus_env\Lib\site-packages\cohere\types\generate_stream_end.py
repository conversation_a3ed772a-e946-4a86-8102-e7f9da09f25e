# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from .finish_reason import FinishReason
from .generate_stream_end_response import GenerateStreamEndResponse
from .generate_stream_event import GenerateStreamEvent


class GenerateStreamEnd(GenerateStreamEvent):
    is_finished: bool
    finish_reason: typing.Optional[FinishReason] = None
    response: GenerateStreamEndResponse

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
