# This file was auto-generated by Fern from our API Definition.

import datetime as dt
import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .api_meta import ApiMeta
from .embed_job_status import EmbedJobStatus
from .embed_job_truncate import EmbedJobTruncate


class EmbedJob(UncheckedBaseModel):
    job_id: str = pydantic.Field()
    """
    ID of the embed job
    """

    name: typing.Optional[str] = pydantic.Field(default=None)
    """
    The name of the embed job
    """

    status: EmbedJobStatus = pydantic.Field()
    """
    The status of the embed job
    """

    created_at: dt.datetime = pydantic.Field()
    """
    The creation date of the embed job
    """

    input_dataset_id: str = pydantic.Field()
    """
    ID of the input dataset
    """

    output_dataset_id: typing.Optional[str] = pydantic.Field(default=None)
    """
    ID of the resulting output dataset
    """

    model: str = pydantic.Field()
    """
    ID of the model used to embed
    """

    truncate: EmbedJobTruncate = pydantic.Field()
    """
    The truncation option used
    """

    meta: typing.Optional[ApiMeta] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
