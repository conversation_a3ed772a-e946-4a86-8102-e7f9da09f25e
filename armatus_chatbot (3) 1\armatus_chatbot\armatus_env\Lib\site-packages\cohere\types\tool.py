# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .tool_parameter_definitions_value import ToolParameterDefinitionsValue


class Tool(UncheckedBaseModel):
    name: str = pydantic.Field()
    """
    The name of the tool to be called. Valid names contain only the characters `a-z`, `A-Z`, `0-9`, `_` and must not begin with a digit.
    """

    description: str = pydantic.Field()
    """
    The description of what the tool does, the model uses the description to choose when and how to call the function.
    """

    parameter_definitions: typing.Optional[typing.Dict[str, ToolParameterDefinitionsValue]] = pydantic.Field(
        default=None
    )
    """
    The input parameters of the tool. Accepts a dictionary where the key is the name of the parameter and the value is the parameter spec. Valid parameter names contain only the characters `a-z`, `A-Z`, `0-9`, `_` and must not begin with a digit.
    ```
    {
      "my_param": {
        "description": <string>,
        "type": <string>, // any python data type, such as 'str', 'bool'
        "required": <boolean>
      }
    }
    ```
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
