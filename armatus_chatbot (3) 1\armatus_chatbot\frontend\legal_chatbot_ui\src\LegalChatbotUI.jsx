import React, { useState, useRef, useEffect } from 'react';
import './LegalChatbot.css';

const API_BASE_URL = 'http://localhost:5000/api';

const LegalChatbotUI = () => {
  const [messages, setMessages] = useState([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [searchMode, setSearchMode] = useState('hybrid');
  
  // New state for document labeling
  const [showLabelModal, setShowLabelModal] = useState(false);
  const [pendingFiles, setPendingFiles] = useState([]);
  const [fileLabels, setFileLabels] = useState({});
  const [documentCategories] = useState([
    'State', 'Manufacture', 'History-QA', 'Other'
  ]);

  
  const [subcategoryOptions] = useState({
    'State': [
      'Alabama', 'Alaska', 'Arizona', 'Arkansas', 'California', 'Colorado', 
      'Connecticut', 'Delaware', 'Florida', 'Georgia', 'Hawaii', 'Idaho', 
      'Illinois', 'Indiana', 'Iowa', 'Kansas', 'Kentucky', 'Louisiana', 
      'Maine', 'Maryland', 'Massachusetts', 'Michigan', 'Minnesota', 
      'Mississippi', 'Missouri', 'Montana', 'Nebraska', 'Nevada', 
      'New Hampshire', 'New Jersey', 'New Mexico', 'New York', 
      'North Carolina', 'North Dakota', 'Ohio', 'Oklahoma', 'Oregon', 
      'Pennsylvania', 'Rhode Island', 'South Carolina', 'South Dakota', 
      'Tennessee', 'Texas', 'Utah', 'Vermont', 'Virginia', 'Washington', 
      'West Virginia', 'Wisconsin', 'Wyoming'
    ],
    'Manufacture': [
      'Ford', 'General Motors (GM)', 'Chevrolet', 'Toyota', 'Honda', 
      'Nissan', 'BMW', 'Mercedes-Benz', 'Audi', 'Volkswagen', 'Hyundai', 
      'Kia', 'Subaru', 'Mazda', 'Volvo', 'Jaguar', 'Land Rover', 
      'Porsche', 'Tesla', 'Chrysler', 'Dodge', 'Jeep', 'Ram', 
      'Cadillac', 'Buick', 'GMC', 'Lincoln', 'Acura', 'Infiniti', 
      'Lexus', 'Tata Motors', 'Mahindra', 'Bajaj', 'Hero MotoCorp', 
      'Maruti Suzuki', 'Other'
    ],
    'History-QA': [
      'Ancient History', 'Medieval History', 'Modern History', 
      'World War I', 'World War II', 'Cold War', 'American History', 
      'European History', 'Asian History', 'African History', 
      'Civil Rights Movement', 'Industrial Revolution', 'Renaissance', 
      'Colonial Period', 'Revolutionary War', 'Civil War', 
      'Great Depression', 'Space Age', 'Digital Age', 'Other'
    ],
    'Other': ['Custom Category']
  });
  const [selectedState, setSelectedState] = useState('');
const [selectedManufacture, setSelectedManufacture] = useState('');

// Extract state and manufacturer lists from subcategoryOptions
const statesList = subcategoryOptions['State'];
const manufacturersList = subcategoryOptions['Manufacture'];
  const [settings, setSettings] = useState({
    embeddingModel: 'sentence_transformers',
    modelName: 'llama-3.3-70b-versatile',
    aiProvider: 'groq',
    topK: 5,
    showSources: true,
    customPrompt: '',
    // API Keys
    openaiApiKey: '',
    cohereApiKey: '',
    huggingfaceApiKey: ''
  });
  const [showSettings, setShowSettings] = useState(false);
  const [serverStatus, setServerStatus] = useState('unknown');
  const [documentStats, setDocumentStats] = useState(null);
  const [showSidebar, setShowSidebar] = useState(true);
  const [modelTestStatus, setModelTestStatus] = useState({});
  const fileInputRef = useRef(null);
  const messagesEndRef = useRef(null);
  
  // Add thread management state
  const [currentThreadId, setCurrentThreadId] = useState(null);
  const [conversationHistory, setConversationHistory] = useState([]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    checkServerHealth();
  }, []);

  const checkServerHealth = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/health`);
      const data = await response.json();
      setServerStatus('healthy');
      if (data.chunks_loaded > 0) {
        setDocumentStats({
          chunks_loaded: data.chunks_loaded,
          files_processed: data.files_processed
        });
      }
    } catch (error) {
      setServerStatus('error');
      addMessage('system', 'Cannot connect to backend server. Please make sure the Flask server is running on http://localhost:5000');
    }
  };

  const testAIModel = async () => {
    try {
      setModelTestStatus({ status: 'testing', message: 'Testing model connection...' });
      
      const response = await fetch(`${API_BASE_URL}/test-model`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          provider: settings.aiProvider,
          model_name: settings.modelName
        })
      });

      const data = await response.json();
      
      if (data.status === 'success') {
        setModelTestStatus({ 
          status: 'success', 
          message: `${settings.aiProvider}/${settings.modelName} is working correctly` 
        });
        addMessage('system', `Model test successful: ${data.message}`);
      } else {
        setModelTestStatus({ 
          status: 'error', 
          message: `Model test failed: ${data.message}` 
        });
        addMessage('system', `Model test failed: ${data.message}`);
      }
    } catch (error) {
      setModelTestStatus({ 
        status: 'error', 
        message: `Connection error: ${error.message}` 
      });
      addMessage('system', `Model test error: ${error.message}`);
    }

    // Clear test status after 5 seconds
    setTimeout(() => {
      setModelTestStatus({});
    }, 5000);
  };
const handleDeleteFile = async (indexToDelete) => {
  const fileToDelete = uploadedFiles[indexToDelete];
  const document_id = fileToDelete.document_id || fileToDelete.name;
  const filename = fileToDelete.name;
  
  try {
    // Show confirmation dialog
    if (window.confirm(`Delete "${fileToDelete.label || fileToDelete.name}"?`)) {
   
      // Call the backend API to delete document
      const response = await fetch(`${API_BASE_URL}/delete-document`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          document_id: document_id,
          filename: filename
        })
      });
      
      const result = await response.json();
      
      if (result.status === 'success') {
        // Remove from UI state only after successful backend deletion
        setUploadedFiles(prevFiles =>
          prevFiles.filter((_, index) => index !== indexToDelete)
        );
        
        // Show success message
        console.log(`✅ ${result.message}`);
        // Optional: Show toast notification
        // showNotification('success', result.message);
        
      } else {
        console.error('❌ Failed to delete:', result.message);
        // showNotification('error', result.message);
      }
    }
    
  } catch (error) {
    console.error('❌ Error deleting file:', error);
    // showNotification('error', 'Failed to delete file');
  }
};


  // Modified file upload handler to show labeling modal
  const handleFileUpload = async (event) => {
    const files = Array.from(event.target.files);
    if (files.length === 0) return;

    // Store files temporarily and show labeling modal
    setPendingFiles(files);
    setShowLabelModal(true);
    
    // Reset file input
    event.target.value = '';
  };

  // New function to handle file labeling and upload
  const handleLabelAndUpload = async () => {
    if (pendingFiles.length === 0) return;

    setIsUploading(true);
    setShowLabelModal(false);
    
    try {
      const formData = new FormData();
      
      // Add files to form data
      pendingFiles.forEach(file => formData.append('files', file));
      
      // Add labels to form data
      const labelsData = {};
      pendingFiles.forEach(file => {
        labelsData[file.name] = fileLabels[file.name] || {
          label: file.name,
          category: 'Other',
          subcategory: '',
          description: ''
        };
      });
      formData.append('file_labels', JSON.stringify(labelsData));

      const response = await fetch(`${API_BASE_URL}/upload`, {
        method: 'POST',
        body: formData
      });

      const data = await response.json();
      
      if (data.status === 'success') {
        const processedFiles = data.files.map(f => ({
          ...f,
          label: labelsData[f.name]?.label || f.name,
          category: labelsData[f.name]?.category || 'Other',
          subcategory: labelsData[f.name]?.subcategory || '',
          description: labelsData[f.name]?.description || ''
        }));
        
        setUploadedFiles(prev => [...prev, ...processedFiles]);
        const fileNames = processedFiles.map(f => f.label || f.name).join(', ');
        addMessage('system', `Files uploaded successfully: ${fileNames}. Click "Process Documents" to analyze them.`);
      } else {
        addMessage('system', `Upload failed: ${data.message}`);
      }
    } catch (error) {
      addMessage('system', `Upload error: ${error.message}`);
    } finally {
      setIsUploading(false);
      setPendingFiles([]);
      setFileLabels({});
    }
  };

  // Function to update file label
  const updateFileLabel = (fileName, field, value) => {
    setFileLabels(prev => ({
      ...prev,
      [fileName]: {
        ...prev[fileName],
        [field]: value,
        // Reset subcategory when category changes
        ...(field === 'category' && value !== prev[fileName]?.category ? { subcategory: '' } : {})
      }
    }));
  };

  // Function to get subcategories based on selected category
  const getSubcategories = (category) => {
    return subcategoryOptions[category] || [];
  };

  // const processDocuments = async () => {
  //   if (uploadedFiles.length === 0) {
  //     addMessage('system', 'No files to process. Please upload documents first.');
  //     return;
  //   }
    
  //   setIsProcessing(true);
  //   addMessage('system', 'Processing documents... This may take a few minutes depending on document size and complexity.');
    
  //   try {
  //     const response = await fetch(`${API_BASE_URL}/process`, {
  //       method: 'POST',
  //       headers: {
  //         'Content-Type': 'application/json'
  //       }
  //     });

  //     const data = await response.json();
  //     if (response.ok && (data.status === 'completed' || data.processed_files > 0)) {
  //     // if (data.status === 'success') {
  //       addMessage('system', `Document processing complete! Created ${data.total_chunks} searchable chunks from ${data.results.length} document(s).`);
        
  //       // Show processing details with labels
  //       const details = data.results.map(r => {
  //         const file = uploadedFiles.find(f => f.name === r.file);
  //         const displayName = file?.label || r.file;
  //         return `• ${displayName}: ${r.chunks} chunks (${r.status})`;
  //       }).join('\n');
  //       addMessage('system', `Processing details:\n${details}`);
        
  //       // Update document stats
  //       setDocumentStats({
  //         chunks_loaded: data.total_chunks,
  //         files_processed: data.results.filter(r => r.status === 'success').length
  //       });
  //     } else {
  //       addMessage('system', `Processing failed: ${data.message}`);
  //     }
  //   } catch (error) {
  //     addMessage('system', `Processing error: ${error.message}`);
  //   } finally {
  //     setIsProcessing(false);
  //   }
  // };


  const processDocuments = async () => {
  if (uploadedFiles.length === 0) {
    addMessage('system', 'No files to process. Please upload documents first.');
    return;
  }
  
  setIsProcessing(true);
  addMessage('system', 'Processing documents... This may take a few minutes depending on document size and complexity.');
  
  try {
    const response = await fetch(`${API_BASE_URL}/process`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const data = await response.json();
    
    if (response.ok && data.status === 'success') {
      // Handle successful processing
      const chunksCount = data.total_chunks || data.chunks_created || 0;
      const processedFiles = data.processed_files || uploadedFiles.length;
      
      addMessage('system', `Document processing complete! Created ${chunksCount} searchable chunks from ${processedFiles} document(s).`);
      
      // Show processing details if available
      if (data.results && Array.isArray(data.results)) {
        // If results is an array, show details with labels
        const details = data.results.map(r => {
          const file = uploadedFiles.find(f => f.name === r.file);
          const displayName = file?.label || r.file;
          return `• ${displayName}: ${r.chunks} chunks (${r.status})`;
        }).join('\n');
        addMessage('system', `Processing details:\n${details}`);
      } else if (data.files_processed && Array.isArray(data.files_processed)) {
        // Alternative format
        const details = data.files_processed.map(filename => {
          const file = uploadedFiles.find(f => f.name === filename);
          const displayName = file?.label || filename;
          return `• ${displayName}: processed successfully`;
        }).join('\n');
        addMessage('system', `Processing details:\n${details}`);
      } else {
        // Generic success message
        const details = uploadedFiles.map(file => {
          return `• ${file.label || file.name}: processed successfully`;
        }).join('\n');
        addMessage('system', `Processing details:\n${details}`);
      }
      
      // Update document stats
      setDocumentStats({
        chunks_loaded: chunksCount,
        files_processed: processedFiles
      });
      
    } else if (response.ok && data.status === 'completed') {
      // Handle alternative success response format
      const chunksCount = data.total_chunks || 0;
      const processedFiles = data.processed_files || uploadedFiles.length;
      
      addMessage('system', `Document processing complete! Created ${chunksCount} searchable chunks from ${processedFiles} document(s).`);
      
      // Update document stats
      setDocumentStats({
        chunks_loaded: chunksCount,
        files_processed: processedFiles
      });
      
    } else {
      // Handle error response
      const errorMessage = data.message || data.error || 'Unknown processing error';
      addMessage('system', `Processing failed: ${errorMessage}`);
      
      // Log additional error details if available
      if (data.details) {
        console.error('Processing error details:', data.details);
      }
    }
    
  } catch (error) {
    console.error('Processing request error:', error);
    addMessage('system', `Processing error: ${error.message}. Please check if the backend server is running and try again.`);
  } finally {
    setIsProcessing(false);
  }
};
  const updateSettings = async (newSettings) => {
    try {
      const response = await fetch(`${API_BASE_URL}/ai-settings`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(newSettings)
      });

      const data = await response.json();
      
      if (data.status === 'success') {
        addMessage('system', `Settings updated successfully`);
        setSettings(newSettings);
      } else {
        addMessage('system', `Settings update failed: ${data.message}`);
      }
    } catch (error) {
      addMessage('system', `Settings update error: ${error.message}`);
    }
  };

  const addMessage = (sender, content, sources = null, metadata = null) => {
    const newMessage = {
      id: Date.now() + Math.random(),
      sender,
      content,
      sources,
      metadata,
      timestamp: new Date().toLocaleTimeString()
    };
    setMessages(prev => [...prev, newMessage]);
  };

  const initializeGroq = async () => {
    try {
      const groqApiKey= '********************************************************'
      if (!groqApiKey) {
        console.warn('GROQ_API_KEY not found in environment variables');
        return false;
      }

      const response = await fetch(`${API_BASE_URL}/groq-settings`, {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({
          groq_api_key: groqApiKey
        })
      });

      const data = await response.json();
      
      if (data.status === 'success') {
        console.log('Groq initialized successfully');
        return true;
      } else {
        console.error('Failed to initialize Groq:', data.message);
        return false;
      }
    } catch (error) {
      console.error('Error initializing Groq:', error);
      return false;
    }
  };

  // const handleSendMessage = async () => {
  //   if (!inputMessage.trim() || isLoading) return;
    
  //   const userMessage = inputMessage.trim();
  //   setInputMessage('');
  //   setIsLoading(true);
    
  //   // Add user message
  //   addMessage('user', userMessage);
    
  //   try {
  //     // Initialize Groq if using Groq provider and not already initialized
  //     if (settings.aiProvider === 'groq') {
  //       const groqInitialized = await initializeGroq();
  //       if (!groqInitialized) {
  //         addMessage('system', 'Failed to initialize Groq. Please check your API key in environment variables.');
  //         return;
  //       }
  //     }

  //     // Match the exact format your Flask API expects
  //     const chatRequest = {
  //       query: userMessage,
  //       top_k: settings.topK,
  //       ai_provider: settings.aiProvider,
  //       ai_model: settings.modelName,
  //       custom_prompt: settings.customPrompt,
  //       thread_id: currentThreadId
  //     };
      
  //     const response = await fetch(`${API_BASE_URL}/chat`, {
  //       method: 'POST',
  //       headers: {
  //         'Content-Type': 'application/json'
  //       },
  //       body: JSON.stringify(chatRequest)
  //     });

  //     const data = await response.json();
      
  //     if (data.status === 'success') {
  //       // Update thread ID if it was created by the backend
  //       if (data.thread_id && !currentThreadId) {
  //         setCurrentThreadId(data.thread_id);
  //       }
        
  //       // Process sources to include document labels
  //       let processedSources = data.sources;
  //       if (settings.showSources && data.sources) {
  //         processedSources = data.sources.map(source => {
  //           const sourceFile = uploadedFiles.find(f => 
  //             source.section && (source.section.includes(f.name) || source.section.includes(f.label))
  //           );
  //           return {
  //             ...source,
  //             documentLabel: sourceFile?.label || null,
  //             documentCategory: sourceFile?.category || null,
  //             documentSubcategory: sourceFile?.subcategory || null
  //           };
  //         });
  //       }
        
  //       // Add assistant response with metadata
  //       addMessage('assistant', data.response, processedSources, {
  //         ai_provider: data.metadata?.ai_provider,
  //         ai_model: data.metadata?.ai_model,
  //         search_results_count: data.metadata?.search_results_count,
  //         thread_id: data.thread_id,
  //         processing_time: data.metadata?.processing_time,
  //         query_length: data.metadata?.query_length,
  //         response_length: data.metadata?.response_length
  //       });
        
  //     } else {
  //       addMessage('system', `Chat error: ${data.message}`);
  //     }
      
  //   } catch (error) {
  //     console.error('Chat request failed:', error);
  //     addMessage('system', `Connection error: ${error.message}. Make sure the backend server is running.`);
  //   } finally {
  //     setIsLoading(false);
  //   }
  // };


  const handleSendMessage = async () => {
  if (!inputMessage.trim() || isLoading) return;
  
  const userMessage = inputMessage.trim();
  setInputMessage('');
  setIsLoading(true);
  
  // Add user message
  addMessage('user', userMessage);
  
  try {
    // Initialize Groq if using Groq provider and not already initialized
    if (settings.aiProvider === 'groq') {
      const groqInitialized = await initializeGroq();
      if (!groqInitialized) {
        addMessage('system', 'Failed to initialize Groq. Please check your API key in environment variables.');
        setIsLoading(false);
        return;
      }
    }

    // Build context string with selected state and manufacture
    let contextString = '';
    if (selectedState) {
      contextString += `State Context: ${selectedState}. `;
    }
    if (selectedManufacture) {
      contextString += `Manufacturer Context: ${selectedManufacture}. `;
    }
    
    // Combine context with user query
    const contextualQuery = contextString ? `${contextString}Query: ${userMessage}` : userMessage;

    // Match the exact format your Flask API expects
    const chatRequest = {
      query: contextualQuery,
      top_k: settings.topK,
      ai_provider: settings.aiProvider,
      ai_model: settings.modelName,
      custom_prompt: settings.customPrompt,
      thread_id: currentThreadId,
      // Add context fields for backend filtering
      selected_state: selectedState || null,
      selected_manufacture: selectedManufacture || null
    };
    
    const response = await fetch(`${API_BASE_URL}/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(chatRequest)
    });

    const data = await response.json();
    
    if (data.status === 'success') {
      // Update thread ID if it was created by the backend
      if (data.thread_id && !currentThreadId) {
        setCurrentThreadId(data.thread_id);
      }
      
      // Process sources to include document labels
      let processedSources = data.sources;
      if (settings.showSources && data.sources) {
        processedSources = data.sources.map(source => {
          const sourceFile = uploadedFiles.find(f => 
            source.section && (source.section.includes(f.name) || source.section.includes(f.label))
          );
          return {
            ...source,
            documentLabel: sourceFile?.label || null,
            documentCategory: sourceFile?.category || null,
            documentSubcategory: sourceFile?.subcategory || null
          };
        });
      }
      
      // Add assistant response with metadata
      addMessage('assistant', data.response, processedSources, {
        ai_provider: data.metadata?.ai_provider,
        ai_model: data.metadata?.ai_model,
        search_results_count: data.metadata?.search_results_count,
        thread_id: data.thread_id,
        processing_time: data.metadata?.processing_time,
        query_length: data.metadata?.query_length,
        response_length: data.metadata?.response_length
      });
      
    } else {
      addMessage('system', `Chat error: ${data.message}`);
    }
    
  } catch (error) {
    console.error('Chat request failed:', error);
    addMessage('system', `Connection error: ${error.message}. Make sure the backend server is running.`);
  } finally {
    setIsLoading(false);
  }
};
  const clearAllData = async () => {
    if (!window.confirm('This will clear all uploaded files and processed data. Continue?')) {
      return;
    }

    try {
      const response = await fetch(`${API_BASE_URL}/clear`, {
        method: 'POST'
      });

      const data = await response.json();
      
      if (data.status === 'success') {
        setUploadedFiles([]);
        setDocumentStats(null);
        setMessages([]);
        addMessage('system', 'All data cleared successfully.');
      } else {
        addMessage('system', `Clear failed: ${data.message}`);
      }
    } catch (error) {
      addMessage('system', `Clear error: ${error.message}`);
    }
  };

  const exportChat = () => {
    const chatData = messages.map(msg => ({
      timestamp: msg.timestamp,
      sender: msg.sender,
      content: msg.content,
      sources: msg.sources,
      metadata: msg.metadata
    }));
    
    const blob = new Blob([JSON.stringify(chatData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `legal-chat-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const ServerStatusIndicator = () => (
    <div className={`status-indicator ${serverStatus === 'healthy' ? 'connected' : serverStatus === 'error' ? 'error' : 'loading'}`}>
      {serverStatus === 'healthy' ? (
        <>
          <span>✓</span>
          <span>Connected</span>
        </>
      ) : serverStatus === 'error' ? (
        <>
          <span>✗</span>
          <span>Disconnected</span>
        </>
      ) : (
        <>
          <div className="animate-spin" style={{
            width: '16px',
            height: '16px',
            border: '2px solid #3b82f6',
            borderTop: '2px solid transparent',
            borderRadius: '50%'
          }}></div>
          <span>Connecting...</span>
        </>
      )}
    </div>
  );

  const MessageBubble = ({ message }) => {
    const isUser = message.sender === 'user';
    const isSystem = message.sender === 'system';
    const messageType = isUser ? 'user' : isSystem ? 'system' : 'assistant';
    
    return (
      <div className={`message ${messageType}`}>
        <div className={`message-bubble ${messageType}`}>
          <div className="message-header">
            <div className={`message-avatar ${messageType}`}>
              <span>{isUser ? '💬' : isSystem ? '⚙️' : '🤖'}</span>
            </div>
            <div className="message-meta">
              <span className="message-sender">
                {isUser ? 'You' : isSystem ? 'System' : 'AI Assistant'}
              </span>
              <span className={`message-timestamp ${messageType}`}>{message.timestamp}</span>
              {message.metadata && message.metadata.generated_by && (
                <span className="model-info" style={{ fontSize: '12px', color: '#6b7280', marginLeft: '8px' }}>
                  ({message.metadata.generated_by}/{message.metadata.model_used})
                </span>
              )}
            </div>
          </div>
          
          <div className="message-content" style={{ whiteSpace: 'pre-wrap' }}>
            {message.content}
          </div>
          
          {message.sources && (
            <div className="sources-section">
              <h4 className="sources-header">
                📄 Sources Used ({message.sources.length}):
              </h4>
              <div className="sources-list">
                {message.sources.map((source, idx) => (
                  <div key={idx} className="source-item">
                    <div className="source-header">
                      <div className="source-section">{source.section}</div>
                      {source.documentLabel && (
                        <div className="source-label">
                          <span className="label-badge">{source.documentLabel}</span>
                          {source.documentCategory && (
                            <span className="category-badge">{source.documentCategory}</span>
                          )}
                          {source.documentSubcategory && (
                            <span className="subcategory-badge">{source.documentSubcategory}</span>
                          )}
                        </div>
                      )}
                    </div>
                    {source.subsection && (
                      <div className="source-subsection">{source.subsection}</div>
                    )}
                    <div className="source-content">{source.content.substring(0, 200)}...</div>
                    <div className="source-relevance">
                      Relevance: {(source.relevanceScore * 100).toFixed(1)}%
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  // Document Labeling Modal Component
  const DocumentLabelModal = () => {
    if (!showLabelModal || pendingFiles.length === 0) return null;

    return (
      <div className="modal-overlay" onClick={() => setShowLabelModal(false)}>
        <div className="modal-content" onClick={(e) => e.stopPropagation()}>
          <div className="modal-header">
            <h3>Label Documents</h3>
            <button 
              className="modal-close" 
              onClick={() => setShowLabelModal(false)}
            >
              ×
            </button>
          </div>
          
          <div className="modal-body">
            <p className="modal-description">
              Add labels and categorize your documents for better organization and reference.
            </p>
            
            <div className="file-labeling-list">
              {pendingFiles.map((file, idx) => (
                <div key={idx} className="file-labeling-item">
                  <div className="file-info">
                    <div className="file-name">{file.name}</div>
                    <div className="file-size">({Math.round(file.size/1024)}KB)</div>
                  </div>
                  
                  <div className="label-inputs">
                    <div className="input-group">
                      <label>Display Label:</label>
                      <input
                        type="text"
                        placeholder={file.name}
                        value={fileLabels[file.name]?.label || ''}
                        onChange={(e) => updateFileLabel(file.name, 'label', e.target.value)}
                        className="label-input"
                      />
                    </div>
                    
                    <div className="input-group">
                      <label>Category:</label>
                      <select
                        value={fileLabels[file.name]?.category || 'Other'}
                        onChange={(e) => updateFileLabel(file.name, 'category', e.target.value)}
                        className="category-select"
                      >
                        {documentCategories.map(cat => (
                          <option key={cat} value={cat}>{cat}</option>
                        ))}
                      </select>
                    </div>

                    {fileLabels[file.name]?.category && getSubcategories(fileLabels[file.name].category).length > 0 && (
                      <div className="input-group">
                        <label>
                          {fileLabels[file.name].category === 'State' ? 'State:' :
                           fileLabels[file.name].category === 'Manufacture' ? 'Manufacturer:' :
                           fileLabels[file.name].category === 'History-QA' ? 'History Topic:' : 'Subcategory:'}
                        </label>
                        <select
                          value={fileLabels[file.name]?.subcategory || ''}
                          onChange={(e) => updateFileLabel(file.name, 'subcategory', e.target.value)}
                          className="category-select"
                        >
                          <option value="">Select {fileLabels[file.name].category === 'State' ? 'State' : 
                                                    fileLabels[file.name].category === 'Manufacture' ? 'Manufacturer' :
                                                    fileLabels[file.name].category === 'History-QA' ? 'Topic' : 'Option'}</option>
                          {getSubcategories(fileLabels[file.name].category).map(subcat => (
                            <option key={subcat} value={subcat}>{subcat}</option>
                          ))}
                        </select>
                      </div>
                    )}

                    {fileLabels[file.name]?.category === 'Other' && (
                      <div className="input-group">
                        <label>Custom Category:</label>
                        <input
                          type="text"
                          placeholder="Enter custom category..."
                          value={fileLabels[file.name]?.subcategory || ''}
                          onChange={(e) => updateFileLabel(file.name, 'subcategory', e.target.value)}
                          className="label-input"
                        />
                      </div>
                    )}
                    
                   
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          <div className="modal-footer">
            <button 
              className="btn btn-secondary" 
              onClick={() => setShowLabelModal(false)}
            >
              Cancel
            </button>
            <button 
              className="btn btn-primary" 
              onClick={handleLabelAndUpload}
              disabled={isUploading}
            >
              {isUploading ? 'Uploading...' : 'Upload & Label'}
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="legal-chatbot-root">
      {/* Document Labeling Modal */}
      <DocumentLabelModal />
      
      {/* Left Sidebar */}
      <div className={`legal-sidebar ${showSidebar ? '' : 'hidden'}`} style={{ width: showSidebar ? '320px' : '0px' }}>
        {/* Sidebar Header */}
        <div style={{ padding: '16px', borderBottom: '1px solid #e2e8f0', backgroundColor: 'white' }}>
          <div className="sidebar-header-content">
            <h1 style={{ fontSize: '18px', fontWeight: '600', color: '#1f2937', margin: '0', display: 'flex', alignItems: 'center', gap: '8px' }}>
              🤖 AI Legal Assistant
            </h1>
          </div>
          <ServerStatusIndicator />
        </div>

        <div className="sidebar-content">
          {/* Upload Documents Section */}
          <div className="card">
            <div className="card-header">
              <h3>📁 Upload Documents</h3>
            </div>
            
            <input
              ref={fileInputRef}
              type="file"
              multiple
              accept=".pdf,.docx,.txt,.xlsx"
              onChange={handleFileUpload}
              className="file-input"
            />
            
            <button
              onClick={() => fileInputRef.current?.click()}
              disabled={isUploading}
              className="btn btn-primary"
              style={{ width: '100%', marginBottom: '12px' }}
            >
              📁 {isUploading ? 'Uploading...' : 'Upload & Label Documents'}
            </button>
            
            {uploadedFiles.length > 0 && (
              <div style={{ marginBottom: '12px' }}>
                <div style={{ fontSize: '14px', color: '#6b7280', marginBottom: '8px' }}>
                  {uploadedFiles.length} file(s) uploaded
                </div>
                <button
                  onClick={processDocuments}
                  disabled={isProcessing}
                  className="btn btn-success"
                  style={{ width: '100%' }}
                >
                  🔍 {isProcessing ? 'Processing...' : 'Process Documents'}
                </button>
              </div>
            )}
            
            {uploadedFiles.length > 0 && (
                
              <div className="file-list">
  {uploadedFiles.map((file, idx) => (
    <div key={idx} className="file-item labeled">
      <div className="file-info">
        <div className="file-main-info">
          <span className="file-label">{file.label || file.name}</span>
          <span className="file-size">({Math.round(file.size/1024)}KB)</span>
        </div>
        {file.category && (
          <div className="file-category">
            <span className="category-tag">{file.category}</span>
            {file.subcategory && (
              <span className="subcategory-tag">{file.subcategory}</span>
            )}
          </div>
        )}
        <div className="file-original-name">Original: {file.name}</div>
      </div>
      
      <button 
        className="file-delete-btn"
        onClick={() => handleDeleteFile(idx)}
        title={`Delete ${file.label || file.name}`}
        aria-label={`Delete ${file.label || file.name}`}
      >
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
          <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
        </svg>
      </button>
    </div>
  ))}
</div>
              // <div className="file-list">
              //   {uploadedFiles.map((file, idx) => (
              //     <div key={idx} className="file-item labeled">
              //       <div className="file-main-info">
              //         <span className="file-label">{file.label || file.name}</span>
              //         <span className="file-size">({Math.round(file.size/1024)}KB)</span>
              //       </div>
              //       {file.category && (
              //         <div className="file-category">
              //           <span className="category-tag">{file.category}</span>
              //           {file.subcategory && (
              //             <span className="subcategory-tag">{file.subcategory}</span>
              //           )}
              //         </div>
              //       )}
              //       {/* {file.description && (
              //         <div className="file-description">{file.description}</div>
              //       )} */}
              //       <div className="file-original-name">Original: {file.name}</div>
              //     </div>
              //   ))}
              // </div>
            )}
          </div>
          
          {/* Search Mode Selection */}
          <div className="card">
            <div className="card-header">
              <h3>🔍 Search Mode</h3>
            </div>
            <select
              value={searchMode}
              onChange={(e) => setSearchMode(e.target.value)}
              className="form-control"
            >
              <option value="hybrid">Hybrid Search (Recommended)</option>
              <option value="semantic">Semantic Search Only</option>
              <option value="keyword">Keyword Search Only</option>
            </select>
          </div>
          
          {/* Settings */}
          <div className="card">
            <button
              onClick={() => setShowSettings(!showSettings)}
              className="settings-toggle"
            >
              <span style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                ⚙️ AI Model Settings
              </span>
              <span className={`settings-toggle-icon ${showSettings ? 'expanded' : ''}`}>
                ▼
              </span>
            </button>
            
            {showSettings && (
              <div className="settings-content">
                {/* Advanced Processing Settings */}
                <div className="form-group">
                  <label className="form-label">Advanced Processing Features</label>
                  
                  <div className="checkbox-group">
                    <input
                      type="checkbox"
                      checked={settings.enableConflictResolution}
                      onChange={(e) => {
                        const newSettings = {...settings, enableConflictResolution: e.target.checked};
                        setSettings(newSettings);
                      }}
                      className="checkbox"
                    />
                    <label>Enable Conflict Resolution</label>
                    <div style={{ fontSize: '11px', color: '#6b7280', marginTop: '2px' }}>
                      Detect and resolve conflicts between state laws and manufacturer rules
                    </div>
                  </div>

                  <div className="checkbox-group">
                    <input
                      type="checkbox"
                      checked={settings.enableContextAwareness}
                      onChange={(e) => {
                        const newSettings = {...settings, enableContextAwareness: e.target.checked};
                        setSettings(newSettings);
                      }}
                      className="checkbox"
                    />
                    <label>Enable Context Awareness</label>
                    <div style={{ fontSize: '11px', color: '#6b7280', marginTop: '2px' }}>
                      Understand nuanced scenarios and conditional statements
                    </div>
                  </div>

                  <div className="checkbox-group">
                    <input
                      type="checkbox"
                      checked={settings.enableMultiSourceLogic}
                      onChange={(e) => {
                        const newSettings = {...settings, enableMultiSourceLogic: e.target.checked};
                        setSettings(newSettings);
                      }}
                      className="checkbox"
                    />
                    <label>Enable Multi-Source Logic</label>
                    <div style={{ fontSize: '11px', color: '#6b7280', marginTop: '2px' }}>
                      Cross-reference multiple sources and apply hierarchical rules
                    </div>
                  </div>
                </div>

                {/* Conflict Resolution Mode */}
                <div className="form-group">
                  <label className="form-label">Conflict Resolution Mode</label>
                  <select
                    value={settings.conflictResolutionMode}
                    onChange={(e) => {
                      const newSettings = {...settings, conflictResolutionMode: e.target.value};
                      setSettings(newSettings);
                    }}
                    className="form-control form-control-sm"
                  >
                    <option value="hierarchical">Hierarchical (Legal Precedence)</option>
                    <option value="weighted">Weighted (Source Reliability)</option>
                    <option value="manual">Manual Review Required</option>
                  </select>
                  <div style={{ fontSize: '12px', color: '#6b7280', marginTop: '4px' }}>
                    How to handle conflicts between different rule sources
                  </div>
                </div>

                {/* Confidence Threshold */}
                <div className="form-group">
                  <label className="form-label">Confidence Threshold</label>
                  <input
                    type="range"
                    min="0.5"
                    max="1.0"
                    step="0.05"
                    value={settings.confidenceThreshold}
                    onChange={(e) => {
                      const newSettings = {...settings, confidenceThreshold: parseFloat(e.target.value)};
                      setSettings(newSettings);
                    }}
                    className="range-input"
                  />
                  <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '12px', color: '#6b7280' }}>
                    <span>Low (50%)</span>
                    <span>Current: {(settings.confidenceThreshold * 100).toFixed(0)}%</span>
                    <span>High (100%)</span>
                  </div>
                  <div style={{ fontSize: '12px', color: '#6b7280', marginTop: '4px' }}>
                    Minimum confidence level required for responses
                  </div>
                </div>

                {/* Custom Prompt */}
                <div className="form-group">
                  <label className="form-label">AI Assistant Prompt</label>
                  <textarea
                    value={settings.customPrompt}
                    onChange={(e) => {
                      const newSettings = {...settings, customPrompt: e.target.value};
                      setSettings(newSettings);
                    }}
                    placeholder="Enter instructions for the AI assistant..."
                    className="form-control form-control-sm"
                    rows="4"
                    style={{ resize: 'vertical' }}
                  />
                  <div style={{ fontSize: '12px', color: '#6b7280', marginTop: '4px' }}>
                    This prompt guides how the AI responds to your questions using the search results.
                  </div>
                </div>
                
                {/* Results Count */}
                <div className="form-group">
                  <label className="form-label">Results to Analyze</label>
                  <input
                    type="number"
                    min="1"
                    max="10"
                    value={settings.topK}
                    onChange={(e) => {
                      const newSettings = {...settings, topK: parseInt(e.target.value)};
                      setSettings(newSettings);
                    }}
                    className="form-control form-control-sm"
                  />
                  <div style={{ fontSize: '12px', color: '#6b7280', marginTop: '4px' }}>
                    Number of document chunks to send to AI for analysis
                  </div>
                </div>

                {/* Show Sources Toggle */}
                <div className="checkbox-group">
                  <input
                    type="checkbox"
                    checked={settings.showSources}
                    onChange={(e) => {
                      const newSettings = {...settings, showSources: e.target.checked};
                      setSettings(newSettings);
                    }}
                    className="checkbox"
                  />
                  <label>Show Source Documents</label>
                </div>

                {/* Action Buttons */}
                <div style={{ display: 'flex', flexDirection: 'column', gap: '8px', marginTop: '16px' }}>
                  <button
                    onClick={() => updateSettings(settings)}
                    className="btn btn-primary"
                    style={{ fontSize: '14px', padding: '8px 12px' }}
                  >
                    💾 Save Settings
                  </button>
                  
                  <button
                    onClick={testAIModel}
                    className="btn btn-secondary"
                    style={{ fontSize: '14px', padding: '8px 12px' }}
                  >
                    🧪 Test AI Model
                  </button>
                  
                  {modelTestStatus.status && (
                    <div style={{ 
                      fontSize: '12px', 
                      padding: '8px', 
                      borderRadius: '4px',
                      backgroundColor: modelTestStatus.status === 'success' ? '#dcfce7' : 
                                      modelTestStatus.status === 'error' ? '#fef2f2' : '#fef3c7',
                      color: modelTestStatus.status === 'success' ? '#166534' : 
                             modelTestStatus.status === 'error' ? '#991b1b' : '#92400e',
                      border: `1px solid ${modelTestStatus.status === 'success' ? '#bbf7d0' : 
                                            modelTestStatus.status === 'error' ? '#fecaca' : '#fde68a'}`
                    }}>
                      {modelTestStatus.message}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
          
          {/* Action Buttons */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            <button
              onClick={exportChat}
              disabled={messages.length === 0}
              className="btn btn-secondary"
            >
              📊 Export Chat
            </button>

            <button
              onClick={() => setMessages([])}
              disabled={messages.length === 0}
              className="btn btn-warning"
            >
              🗑️ Clear Chat
            </button>

            <button
              onClick={clearAllData}
              className="btn btn-danger"
            >
              🗑️ Clear All Data
            </button>
          </div>
          
          <div className="quick-start">
            <div className="quick-start-header">
              🚀 Quick Start:
            </div>
            <div className="quick-start-list">
              <div className="quick-start-item" style={{marginLeft: '-27px'}}>1. Upload PDF, DOCX, TXT, or XLSX documents</div>
              <div className="quick-start-item">2. Add labels and categories to your documents</div>
              <div className="quick-start-item">3. Click "Process Documents"</div>
              <div className="quick-start-item">4. Configure AI model settings</div>
              <div className="quick-start-item" style={{marginLeft: '-20px'}}>5. Ask questions and get AI-powered answers</div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Chat Area */}
      <div className="legal-main-area">
        {/* Chat Header */}
        <div className="top-header">
          <div className="top-header-left">
            <div className="top-header-title">
              <h2>AI-Powered Legal Assistant</h2>
            </div>
            <button
              onClick={() => setShowSidebar(!showSidebar)}
              style={{
                padding: '4px 12px',
                color: '#3b82f6',
                background: 'none',
                border: 'none',
                fontSize: '14px',
                fontWeight: '500',
                cursor: 'pointer'
              }}
            >
              {showSidebar ? '← Hide Panel' : '→ Show Panel'}
            </button>
          </div>
          <div className="top-header-right">
            <span className="status-badge search-mode">
              {searchMode.charAt(0).toUpperCase() + searchMode.slice(1)} Search
            </span>
            <span className="status-badge ai-provider">
              {settings.aiProvider.toUpperCase()} AI
            </span>
            {documentStats && (
              <span className="status-badge chunks-ready">
                {documentStats.chunks_loaded} chunks ready
              </span>
            )}
          </div>
        </div>

        {/* Chat Messages */}
        <div className="messages-area" style={{ backgroundColor: '#f9fafb' }}>
          {messages.length === 0 ? (
            <div className="welcome-screen">
              <div className="welcome-content">
                <div className="welcome-icon">
                  <span style={{ fontSize: '32px' }}>🤖</span>
                </div>
                <h3 className="welcome-title">Welcome to AI Legal Assistant</h3>
                <p className="welcome-subtitle">
                  Upload documents, add labels for better organization, configure your AI model, and get intelligent answers.<br />
                  I'll analyze your labeled documents and provide direct responses using advanced AI.
                </p>

                {!documentStats && serverStatus === 'healthy' && (
                  <div className="info-card">
                    <div className="info-card-header">
                      <h4>🚀 Getting Started:</h4>
                    </div>
                    <ol className="info-card-list">
                      <li className="info-card-item">
                        <span className="info-card-number">1</span>
                        Upload PDF, DOCX, TXT, or XLSX documents
                      </li>
                      <li className="info-card-item">
                        <span className="info-card-number">2</span>
                        Add descriptive labels and categories to your documents
                      </li>
                      <li className="info-card-item">
                        <span className="info-card-number">3</span>
                        Click "Process Documents" to analyze them
                      </li>
                      <li className="info-card-item">
                        <span className="info-card-number">4</span>
                        Configure AI settings (Groq, OpenAI, etc.)
                      </li>
                      <li className="info-card-item">
                        <span className="info-card-number">5</span>
                        Ask questions and get AI-powered answers with source references
                      </li>
                    </ol>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="messages-container">
              {messages.map((message) => (
                <MessageBubble key={message.id} message={message} />
              ))}
              {isLoading && (
                <div className="loading-message">
                  <div className="loading-bubble">
                    <div className="loading-content">
                      <div className="animate-spin" style={{
                        width: '16px',
                        height: '16px',
                        border: '2px solid #3b82f6',
                        borderTop: '2px solid transparent',
                        borderRadius: '50%'
                      }}></div>
                      <span className="loading-text">
                        AI is analyzing documents ({settings.aiProvider}/{settings.modelName})...
                      </span>
                    </div>
                  </div>
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>
          )}
        </div>
        


        {/* Input Area */}
<div className="input-area">
  {/* Context Selection Dropdowns - Above Chat Input */}
  <div className="context-selection-bar">
    <div className="context-dropdowns">
      {/* State Selection */}
      <div className="context-dropdown-group">
        <label className="context-label">State:</label>
        <select
          value={selectedState}
          onChange={(e) => setSelectedState(e.target.value)}
          className="context-select"
        >
          <option value="">Select State</option>
          {statesList.map(state => (
            <option key={state} value={state}>{state}</option>
          ))}
        </select>
      </div>
      
      {/* Manufacturer Selection */}
      <div className="context-dropdown-group">
        <label className="context-label">Manufacturer:</label>
        <select
          value={selectedManufacture}
          onChange={(e) => setSelectedManufacture(e.target.value)}
          className="context-select"
        >
          <option value="">Select Manufacturer</option>
          {manufacturersList.map(manufacturer => (
            <option key={manufacturer} value={manufacturer}>{manufacturer}</option>
          ))}
        </select>
      </div>
      
      {/* Clear Context Button */}
      {(selectedState || selectedManufacture) && (
        <button
          onClick={() => {
            setSelectedState('');
            setSelectedManufacture('');
          }}
          className="context-clear-btn"
          title="Clear selected context"
        >
          ✕
        </button>
      )}
    </div>
  </div>
  
  <div className="input-container">
    {/* Rest of input area code... */}









        {/* Input Area */}
        <div className="input-area">
          <div className="input-container">
            <div className="input-row">
              <input
                type="text"
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && !e.shiftKey && handleSendMessage()}
                placeholder={
                  documentStats 
                    ? "Ask your AI legal assistant..." 
                    : "Upload and process documents first..."
                }
                className="message-input"
                disabled={isLoading || !documentStats}
              />
              <button
                onClick={handleSendMessage}
                disabled={!inputMessage.trim() || isLoading || !documentStats}
                className="send-button"
              >
                🤖 <span className="send-button-text">Ask AI</span>
              </button>
            </div>
            
            {!documentStats && (
              <div className="input-help">
                Upload and label documents first to enable AI chat functionality
              </div>
            )}
            
            {documentStats && (
              <div className="input-help" style={{ color: '#6b7280', fontSize: '12px' }}>
                AI Model: {settings.aiProvider}/{settings.modelName} • {documentStats.chunks_loaded} chunks available • {uploadedFiles.length} labeled documents
              </div>
            )}
          </div>
        </div>
      </div>

      <style jsx>{`
        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1000;
        }

        .modal-content {
          background: white;
          border-radius: 8px;
          max-width: 600px;
          width: 90%;
          max-height: 80vh;
          overflow-y: auto;
          box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }

        .modal-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 20px 24px 16px;
          border-bottom: 1px solid #e5e7eb;
        }

        .modal-header h3 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: #1f2937;
        }

        .modal-close {
          background: none;
          border: none;
          font-size: 24px;
          color: #6b7280;
          cursor: pointer;
          padding: 0;
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 4px;
        }

        .modal-close:hover {
          background-color: #f3f4f6;
          color: #374151;
        }

        .modal-body {
          padding: 16px 24px;
        }

        .modal-description {
          margin: 0 0 20px 0;
          color: #6b7280;
          font-size: 14px;
        }

        .file-labeling-list {
          display: flex;
          flex-direction: column;
          gap: 20px;
        }

        .file-labeling-item {
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          padding: 16px;
          background-color: #f9fafb;
        }

        .file-info {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;
        }

        .file-name {
          font-weight: 500;
          color: #1f2937;
          word-break: break-all;
        }

        .file-size {
          color: #6b7280;
          font-size: 12px;
          white-space: nowrap;
        }

        .label-inputs {
          display: flex;
          flex-direction: column;
          gap: 12px;
        }

        .input-group {
          display: flex;
          flex-direction: column;
          gap: 4px;
        }

        .input-group label {
          font-size: 12px;
          font-weight: 500;
          color: #374151;
        }

        .label-input,
        .description-input {
          padding: 8px 12px;
          border: 1px solid #d1d5db;
          border-radius: 4px;
          font-size: 14px;
        }

        .category-select {
          padding: 8px 12px;
          border: 1px solid #d1d5db;
          border-radius: 4px;
          font-size: 14px;
          background-color: white;
        }

        .label-input:focus,
        .description-input:focus,
        .category-select:focus {
          outline: none;
          border-color: #3b82f6;
          ring: 2px;
          ring-color: rgba(59, 130, 246, 0.1);
        }

        .modal-footer {
          display: flex;
          justify-content: flex-end;
          gap: 12px;
          padding: 16px 24px 20px;
          border-top: 1px solid #e5e7eb;
        }

        .file-item.labeled {
          background-color: #f8fafc;
          border-left: 4px solid #3b82f6;
          padding-left: 12px;
        }

        .file-main-info {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 4px;
        }

        .file-label {
          font-weight: 500;
          color: #1e40af;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .file-category {
          margin-bottom: 4px;
        }

        .category-tag {
          background-color: #dbeafe;
          color: #1e40af;
          padding: 2px 6px;
          border-radius: 12px;
          font-size: 11px;
          font-weight: 500;
        }

        .subcategory-tag {
          background-color: #059669;
          color: white;
          padding: 2px 6px;
          border-radius: 12px;
          font-size: 10px;
          font-weight: 500;
          margin-left: 4px;
        }

        .file-description {
          font-size: 12px;
          color: #6b7280;
          margin-bottom: 4px;
          font-style: italic;
        }

        .file-original-name {
          font-size: 11px;
          color: #9ca3af;
        }

        .source-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 8px;
        }

        .source-label {
          display: flex;
          flex-direction: column;
          gap: 4px;
          align-items: flex-end;
        }

        .label-badge {
          background-color: #3b82f6;
          color: white;
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 11px;
          font-weight: 500;
        }

        .category-badge {
          background-color: #10b981;
          color: white;
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 10px;
          font-weight: 500;
        }

        .subcategory-badge {
          background-color: #8b5cf6;
          color: white;
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 10px;
          font-weight: 500;
        }
  .file-list {
    margin-top: 8px;
  }

  .file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    margin-bottom: 4px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    border-left: 4px solid #007bff;
    transition: all 0.2s ease;
  }

  .file-item:hover {
    background: #f1f3f4;
    border-left-color: #0056b3;
  }

  .file-info {
    flex: 1;
    min-width: 0;
  }

  .file-main-info {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 2px;
  }

  .file-label {
    font-weight: 500;
    color: #2c3e50;
    font-size: 14px;
  }

  .file-size {
    color: #6c757d;
    font-size: 12px;
  }

  .file-category {
    display: flex;
    gap: 6px;
    margin-bottom: 2px;
  }

  .category-tag {
    background: #e3f2fd;
    color: #1976d2;
    padding: 2px 6px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 500;
  }

  .subcategory-tag {
    background: #f3e5f5;
    color: #7b1fa2;
    padding: 2px 6px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 500;
  }

  .file-original-name {
    color: #6c757d;
    font-size: 11px;
    font-style: italic;
  }

  .file-delete-btn {
    background: none;
    border: none;
    color: #dc3545;
    cursor: pointer;
    padding: 6px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    flex-shrink: 0;
    margin-left: 12px;
  }

  .file-delete-btn:hover {
    background: #f8d7da;
    color: #721c24;
  }

  .file-delete-btn:focus {
    outline: 2px solid #dc3545;
    outline-offset: 2px;
  }
.context-selection-bar {
  background: white;
  border-top: 1px solid #e5e7eb;
  padding: 12px 16px;
  border-radius: 8px 8px 0 0;
}

.context-dropdowns {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.context-dropdown-group {
  display: flex;
  align-items: center;
  gap: 6px;
}

.context-label {
  font-size: 12px;
  font-weight: 500;
  color: #4b5563;
  white-space: nowrap;
}

.context-select {
  padding: 6px 10px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 13px;
  background-color: white;
  min-width: 140px;
  transition: all 0.15s ease-in-out;
}

.context-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.context-clear-btn {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
}

.context-clear-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
  .context-dropdowns {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .context-dropdown-group {
    justify-content: space-between;
  }
  
  .context-select {
    min-width: auto;
    flex: 1;
    margin-left: 8px;
  }
}
  .file-delete-btn svg {
    pointer-events: none;
  }

      `}</style>
    </div>
  );
};

export default LegalChatbotUI;