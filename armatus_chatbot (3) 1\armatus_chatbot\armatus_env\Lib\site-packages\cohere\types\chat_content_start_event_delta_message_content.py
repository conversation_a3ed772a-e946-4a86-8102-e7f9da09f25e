# This file was auto-generated by <PERSON>rn from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .chat_content_start_event_delta_message_content_type import ChatContentStartEventDeltaMessageContentType


class ChatContentStartEventDeltaMessageContent(UncheckedBaseModel):
    text: typing.Optional[str] = None
    type: typing.Optional[ChatContentStartEventDeltaMessageContentType] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
