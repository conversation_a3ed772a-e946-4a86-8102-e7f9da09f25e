cohere-5.17.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
cohere-5.17.0.dist-info/LICENSE,sha256=RNrMyrG7SWRbPG3vPAySqqG7yi4Eia85iywiloIEcBU,1062
cohere-5.17.0.dist-info/METADATA,sha256=ahq5UHj411FE6GbkKPRNBt0hDR-G7bHJh4RXm-V_DO0,3439
cohere-5.17.0.dist-info/RECORD,,
cohere-5.17.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
cohere-5.17.0.dist-info/WHEEL,sha256=Zb28QaM1gQi8f4VCBhsUklF61CTlNYfs9YAZn-TOGFk,88
cohere/__init__.py,sha256=x_1IcM3YoMHkwn9mWZrBTofhmqCVjmSDJ5xzrLP6-Bc,17025
cohere/__pycache__/__init__.cpython-39.pyc,,
cohere/__pycache__/aliases.cpython-39.pyc,,
cohere/__pycache__/aws_client.cpython-39.pyc,,
cohere/__pycache__/base_client.cpython-39.pyc,,
cohere/__pycache__/bedrock_client.cpython-39.pyc,,
cohere/__pycache__/client.cpython-39.pyc,,
cohere/__pycache__/client_v2.cpython-39.pyc,,
cohere/__pycache__/config.cpython-39.pyc,,
cohere/__pycache__/environment.cpython-39.pyc,,
cohere/__pycache__/overrides.cpython-39.pyc,,
cohere/__pycache__/raw_base_client.cpython-39.pyc,,
cohere/__pycache__/sagemaker_client.cpython-39.pyc,,
cohere/__pycache__/utils.cpython-39.pyc,,
cohere/__pycache__/version.cpython-39.pyc,,
cohere/aliases.py,sha256=EGDi_vuEZrmLpA9TTs4uWoX7bFSnjkIkGKUbwK0eG0Q,1013
cohere/aws_client.py,sha256=DXWCn1dXkFl1MmCjMgEMNYxJB8m-89xBmxwr4DgqtIQ,10127
cohere/base_client.py,sha256=bP9XOwMKydkUyDuXvp3fhHvAy-OHrIB16NS7M_mIiYY,156379
cohere/bedrock_client.py,sha256=5j_h_Ur7cWSEvkIaWTxSyL0QPlJzi71D2vROCVaS8C8,1755
cohere/client.py,sha256=ghQPgpNjWM9fMQri2EpPVlBfityjvfHREvpxLUDST3M,23698
cohere/client_v2.py,sha256=5i2O4hq2tzd9YCRblfucjnS6poRd4AgWESbhggdpv0k,2555
cohere/config.py,sha256=LXkR_mbbgDlXuVwrc6MqiRl7pOMydtel4EeewNpHC88,22
cohere/connectors/__init__.py,sha256=_VhToAyIt_5axN6CLJwtxg3-CO7THa_23pbUzqhXJa4,85
cohere/connectors/__pycache__/__init__.cpython-39.pyc,,
cohere/connectors/__pycache__/client.cpython-39.pyc,,
cohere/connectors/__pycache__/raw_client.cpython-39.pyc,,
cohere/connectors/client.py,sha256=nCh6wetTsdtgEMrZbBjkNG3WGHotuyueo7NVTvtbO28,21589
cohere/connectors/raw_client.py,sha256=FUZky4XiBk8Yc1CFp15le_tE93kzB8LVthEMQSoBOUo,98463
cohere/core/__init__.py,sha256=tpn7rjb6C2UIkYZYIqdrNpI7Yax2jw88sXh2baxaxAI,1715
cohere/core/__pycache__/__init__.cpython-39.pyc,,
cohere/core/__pycache__/api_error.cpython-39.pyc,,
cohere/core/__pycache__/client_wrapper.cpython-39.pyc,,
cohere/core/__pycache__/datetime_utils.cpython-39.pyc,,
cohere/core/__pycache__/file.cpython-39.pyc,,
cohere/core/__pycache__/force_multipart.cpython-39.pyc,,
cohere/core/__pycache__/http_client.cpython-39.pyc,,
cohere/core/__pycache__/http_response.cpython-39.pyc,,
cohere/core/__pycache__/jsonable_encoder.cpython-39.pyc,,
cohere/core/__pycache__/pydantic_utilities.cpython-39.pyc,,
cohere/core/__pycache__/query_encoder.cpython-39.pyc,,
cohere/core/__pycache__/remove_none_from_dict.cpython-39.pyc,,
cohere/core/__pycache__/request_options.cpython-39.pyc,,
cohere/core/__pycache__/serialization.cpython-39.pyc,,
cohere/core/__pycache__/unchecked_base_model.cpython-39.pyc,,
cohere/core/api_error.py,sha256=44vPoTyWN59gonCIZMdzw7M1uspygiLnr3GNFOoVL2Q,614
cohere/core/client_wrapper.py,sha256=IGEwI-r8DkvsmLhu6CKtqRrTdgTejPBhzCjmqUYUMOU,2567
cohere/core/datetime_utils.py,sha256=nBys2IsYrhPdszxGKCNRPSOCwa-5DWOHG95FB8G9PKo,1047
cohere/core/file.py,sha256=d4NNbX8XvXP32z8KpK2Xovv33nFfruIrpz0QWxlgpZk,2663
cohere/core/force_multipart.py,sha256=awxh5MtcRYe74ehY8U76jzv6fYM_w_D3Rur7KQQzSDk,429
cohere/core/http_client.py,sha256=QurkBvCZZz2Z1d8znp4M2YbOXebBUPcPXRhPIS84Wvk,21214
cohere/core/http_response.py,sha256=4uOAtXXFTyFXHLXeQWSfQST9PGcOCRAdHVgGTxdyg84,1334
cohere/core/jsonable_encoder.py,sha256=hGgcEEeX11sqxxsll7h15pO3pTNVxk_n79Kcn0laoWA,3655
cohere/core/pydantic_utilities.py,sha256=HxbbISfaP1XBvzbIkc0ZcF_GHKd9BfYsJAcFh9B126k,10787
cohere/core/query_encoder.py,sha256=ekulqNd0j8TgD7ox-Qbz7liqX8-KP9blvT9DsRCenYM,2144
cohere/core/remove_none_from_dict.py,sha256=EU9SGgYidWq7SexuJbNs4-PZ-5Bl3Vppd864mS6vQZw,342
cohere/core/request_options.py,sha256=h0QUNCFVdCW_7GclVySCAY2w4NhtXVBUCmHgmzaxpcg,1681
cohere/core/serialization.py,sha256=ECL3bvv_0i7U4uvPidZCNel--MUbA0iq0aGcNKi3kws,9818
cohere/core/unchecked_base_model.py,sha256=CskE9m-ZNvMg3AjGwuBqCaT1CbfY7sVpOVWr0FfOLDQ,10752
cohere/datasets/__init__.py,sha256=fLRZXsanS7C4IUSbH3QniEqyyYgodaqnOYsZtKMurok,309
cohere/datasets/__pycache__/__init__.cpython-39.pyc,,
cohere/datasets/__pycache__/client.cpython-39.pyc,,
cohere/datasets/__pycache__/raw_client.cpython-39.pyc,,
cohere/datasets/client.py,sha256=Gp2zOTCaZ-uXNLZl8SbHfXVfePvivpCKoYDcNl-ziDM,19320
cohere/datasets/raw_client.py,sha256=wlaJUpg4nC_zZLU_1bfisDcAtbO9MIneMRF2f_524Xg,82637
cohere/datasets/types/__init__.py,sha256=jpVMP9IvtjgkNjsJTTxwFRxVmecPztgZD4brPdsfFGE,437
cohere/datasets/types/__pycache__/__init__.cpython-39.pyc,,
cohere/datasets/types/__pycache__/datasets_create_response.cpython-39.pyc,,
cohere/datasets/types/__pycache__/datasets_get_response.cpython-39.pyc,,
cohere/datasets/types/__pycache__/datasets_get_usage_response.cpython-39.pyc,,
cohere/datasets/types/__pycache__/datasets_list_response.cpython-39.pyc,,
cohere/datasets/types/datasets_create_response.py,sha256=-Sn1Qf_sVEoVE7l3KsxtJegL-d7IgBGQWNr27S64ENU,612
cohere/datasets/types/datasets_get_response.py,sha256=GeQgrx9UszkZktxkS8t-inATaQgYntpjC96aj4P9XU0,572
cohere/datasets/types/datasets_get_usage_response.py,sha256=zfkWlR-R23d8w84lrsO-yTt4szUkULs50I6nMfrxGs4,667
cohere/datasets/types/datasets_list_response.py,sha256=ZoiAc9aNTkiKKj6qpldxz0t5Gi78Yq1uzuQmz7cXsnE,611
cohere/embed_jobs/__init__.py,sha256=hEYbmiDCQUyWN6cujt2wqYXnylUtedOIfRRDYVWVqPI,179
cohere/embed_jobs/__pycache__/__init__.cpython-39.pyc,,
cohere/embed_jobs/__pycache__/client.cpython-39.pyc,,
cohere/embed_jobs/__pycache__/raw_client.cpython-39.pyc,,
cohere/embed_jobs/client.py,sha256=vVvRTp2VtKll1nBotU_UbHWILpyz4S1yNY98ZslzlRs,14373
cohere/embed_jobs/raw_client.py,sha256=-z0okQZc0OomO_hLf8GRjttFTkv3rwVsIT1ZOLu9zOk,63932
cohere/embed_jobs/types/__init__.py,sha256=Y7X7Q_-KiiyHgWy1FGHHHOihLDdNJEKaBU0eVyFB4gM,207
cohere/embed_jobs/types/__pycache__/__init__.cpython-39.pyc,,
cohere/embed_jobs/types/__pycache__/create_embed_job_request_truncate.cpython-39.pyc,,
cohere/embed_jobs/types/create_embed_job_request_truncate.py,sha256=KP9V8Jyn2_bsO-1w4XQ-CBPz4qqX9NCoKoYzah95ekQ,169
cohere/environment.py,sha256=CMhfszuC6jOD46XjdhYPXmvxrli5d7-1W9rdbB4iN3I,157
cohere/errors/__init__.py,sha256=00BzHG8BtR8xLU0-4G6t3Gra1aXdPePBqH-Y72w5JpE,1072
cohere/errors/__pycache__/__init__.cpython-39.pyc,,
cohere/errors/__pycache__/bad_request_error.cpython-39.pyc,,
cohere/errors/__pycache__/client_closed_request_error.cpython-39.pyc,,
cohere/errors/__pycache__/forbidden_error.cpython-39.pyc,,
cohere/errors/__pycache__/gateway_timeout_error.cpython-39.pyc,,
cohere/errors/__pycache__/internal_server_error.cpython-39.pyc,,
cohere/errors/__pycache__/invalid_token_error.cpython-39.pyc,,
cohere/errors/__pycache__/not_found_error.cpython-39.pyc,,
cohere/errors/__pycache__/not_implemented_error.cpython-39.pyc,,
cohere/errors/__pycache__/service_unavailable_error.cpython-39.pyc,,
cohere/errors/__pycache__/too_many_requests_error.cpython-39.pyc,,
cohere/errors/__pycache__/unauthorized_error.cpython-39.pyc,,
cohere/errors/__pycache__/unprocessable_entity_error.cpython-39.pyc,,
cohere/errors/bad_request_error.py,sha256=PnE3v3kETCXm9E3LiNcHLNtjPEUvpe98-r59q-kQb78,338
cohere/errors/client_closed_request_error.py,sha256=6cP9dJxqXqkV93eEG3UbPxd7jVSZNP_I8q4SQGaa5BQ,347
cohere/errors/forbidden_error.py,sha256=JhKThpM90vF0BEmaBn-8P_0NVYmgJ2BE9kvWmLxU_nA,337
cohere/errors/gateway_timeout_error.py,sha256=3xwRsEXwPCEFtM6PYplQ8kezmr1mOfmA7JjkeKOngwc,342
cohere/errors/internal_server_error.py,sha256=t1-kpoDC2biEuoE-Ne8v1kuQswvsIEwt_xPPoBmGG00,342
cohere/errors/invalid_token_error.py,sha256=OzQ8lHZAUHdEd0p4QdDge3rEycIfWSEZOGb1wxiGT30,340
cohere/errors/not_found_error.py,sha256=YrqVM0oc3qkQbFbmmm6xr300VGfUNxMSy1UQUp2IOE8,336
cohere/errors/not_implemented_error.py,sha256=E_FFObYnoOIvwtVEhZlv_EllZ-G1ZwCJhtU7rqRxk6Q,342
cohere/errors/service_unavailable_error.py,sha256=MIoOqruDBRDJtJJFK45O4_Xwjcwh4diwKbpHlqNQ-ZI,346
cohere/errors/too_many_requests_error.py,sha256=Dl-_pfpboXJh-OtSbRaPQOB-UXvpVOElRDgjxbi4j7w,343
cohere/errors/unauthorized_error.py,sha256=mryinHCAaknn5St2VF17R9XybZUcWRRYWEjxg63dQSA,340
cohere/errors/unprocessable_entity_error.py,sha256=JqxtzIhvjkpQDqbT9Q-go1n-gyv9PsYqq0ng_ZYyBMo,347
cohere/finetuning/__init__.py,sha256=WJqo7zhdzC39uAmy156AS59R52JHqHXGFi34D8bXfGo,1033
cohere/finetuning/__pycache__/__init__.cpython-39.pyc,,
cohere/finetuning/__pycache__/client.cpython-39.pyc,,
cohere/finetuning/__pycache__/raw_client.cpython-39.pyc,,
cohere/finetuning/client.py,sha256=SteTySWmmMiZB6VBcOD9DtKtwZSFpnoZGDDLLa_RXwo,24148
cohere/finetuning/finetuning/__init__.py,sha256=EBf2lsYVeZUCqSjtChgD-iMJoVrqwmIw5iLpD5BIKgs,985
cohere/finetuning/finetuning/__pycache__/__init__.cpython-39.pyc,,
cohere/finetuning/finetuning/types/__init__.py,sha256=M0go0jE1XdfVJ-d5DBnkXb-I9XiU09P0Nxl0viS7uXs,1458
cohere/finetuning/finetuning/types/__pycache__/__init__.cpython-39.pyc,,
cohere/finetuning/finetuning/types/__pycache__/base_model.cpython-39.pyc,,
cohere/finetuning/finetuning/types/__pycache__/base_type.cpython-39.pyc,,
cohere/finetuning/finetuning/types/__pycache__/create_finetuned_model_response.cpython-39.pyc,,
cohere/finetuning/finetuning/types/__pycache__/delete_finetuned_model_response.cpython-39.pyc,,
cohere/finetuning/finetuning/types/__pycache__/event.cpython-39.pyc,,
cohere/finetuning/finetuning/types/__pycache__/finetuned_model.cpython-39.pyc,,
cohere/finetuning/finetuning/types/__pycache__/get_finetuned_model_response.cpython-39.pyc,,
cohere/finetuning/finetuning/types/__pycache__/hyperparameters.cpython-39.pyc,,
cohere/finetuning/finetuning/types/__pycache__/list_events_response.cpython-39.pyc,,
cohere/finetuning/finetuning/types/__pycache__/list_finetuned_models_response.cpython-39.pyc,,
cohere/finetuning/finetuning/types/__pycache__/list_training_step_metrics_response.cpython-39.pyc,,
cohere/finetuning/finetuning/types/__pycache__/lora_target_modules.cpython-39.pyc,,
cohere/finetuning/finetuning/types/__pycache__/settings.cpython-39.pyc,,
cohere/finetuning/finetuning/types/__pycache__/status.cpython-39.pyc,,
cohere/finetuning/finetuning/types/__pycache__/strategy.cpython-39.pyc,,
cohere/finetuning/finetuning/types/__pycache__/training_step_metrics.cpython-39.pyc,,
cohere/finetuning/finetuning/types/__pycache__/update_finetuned_model_response.cpython-39.pyc,,
cohere/finetuning/finetuning/types/__pycache__/wandb_config.cpython-39.pyc,,
cohere/finetuning/finetuning/types/base_model.py,sha256=BJcFrm0LvoosJoyS3Kil7uespQ8BTkG7CNWIhkc0U_U,1087
cohere/finetuning/finetuning/types/base_type.py,sha256=Uxgv2LGIktCXDK4rsqQWAIhl3oG7GkdDFZa2qMNt1pk,305
cohere/finetuning/finetuning/types/create_finetuned_model_response.py,sha256=tkkUzxFi-b7PjsNkAS69rsImREhz8ex33Y2_1_ACK6I,784
cohere/finetuning/finetuning/types/delete_finetuned_model_response.py,sha256=GjNpRdC1NAlPxplKt7CmtrstTp6KtMsG8r7HCsUQZCA,157
cohere/finetuning/finetuning/types/event.py,sha256=dPBgfod9D45Z0anPvyOxhboPBWc5BQoyxtgcvwqx3Cs,1025
cohere/finetuning/finetuning/types/finetuned_model.py,sha256=YlPN9AY9gw2aN_O7c8JBVXZ3b75yuHQSDQ3A2Fmd0L4,1973
cohere/finetuning/finetuning/types/get_finetuned_model_response.py,sha256=OBWUdrKW8py2gZV0WxqQqUNNW5hwPrcTcBfdUJsl8qw,780
cohere/finetuning/finetuning/types/hyperparameters.py,sha256=VO2br9iX_VvPZgnKL1pu2L5rybdYBmG1T7ErJqrf8Pw,2009
cohere/finetuning/finetuning/types/list_events_response.py,sha256=lXgByhMR64rpsuYk1MA6Yb00rlVLwYMH-9hYKqXddzw,1092
cohere/finetuning/finetuning/types/list_finetuned_models_response.py,sha256=adK8D_Nsx2hamOqV1HMP2OKMUUJHbM3_ZAOXViL-KfQ,1135
cohere/finetuning/finetuning/types/list_training_step_metrics_response.py,sha256=JIXMWPcJXMixpG0uXPl4j51A_niqmwC10RIZPG03GH0,1069
cohere/finetuning/finetuning/types/lora_target_modules.py,sha256=ai6LQtp7DSyvHBlJsdPryDbGaygD3WztMnw2Mkc7LUY,312
cohere/finetuning/finetuning/types/settings.py,sha256=VlONUZL-19HMGgjyRXweOIH1s1Qz4tlyitPxzZ7z-X0,1367
cohere/finetuning/finetuning/types/status.py,sha256=m9UeyT56xh3bE3VdGfzjbY2YybdfgOusjIkX6imZvxQ,402
cohere/finetuning/finetuning/types/strategy.py,sha256=-yCe8aLg5bwXsPd02MjiN4Za-uyPVt_AT6h-8yzqkQM,193
cohere/finetuning/finetuning/types/training_step_metrics.py,sha256=eR28MidcL8Gh6YcbML3KoKd56viTLcpaf3NNO1Oibu0,1015
cohere/finetuning/finetuning/types/update_finetuned_model_response.py,sha256=pDEVHvXhQidZF64fOtK-EIcOCZZ-RgwN2Ssgck1Oy1U,786
cohere/finetuning/finetuning/types/wandb_config.py,sha256=7OiONoJon8x0yN5uBWwUaG1Cvn4AU6KNAWi3IPitqLg,910
cohere/finetuning/raw_client.py,sha256=dEseOStwFmF9-_BrR8KcBDSzcTJxJpfko8MQ_P-AvKw,70991
cohere/manually_maintained/__pycache__/cache.cpython-39.pyc,,
cohere/manually_maintained/__pycache__/lazy_aws_deps.cpython-39.pyc,,
cohere/manually_maintained/__pycache__/tokenizers.cpython-39.pyc,,
cohere/manually_maintained/cache.py,sha256=fu33e6anRa62oIlmOZoh_Lexu4fYq0l-KF5P8pD4ssY,811
cohere/manually_maintained/cohere_aws/__init__.py,sha256=YGytjCZUtdsCX7rIumA2LVv-MudhTsKAhqmgVl5jBVo,81
cohere/manually_maintained/cohere_aws/__pycache__/__init__.cpython-39.pyc,,
cohere/manually_maintained/cohere_aws/__pycache__/chat.cpython-39.pyc,,
cohere/manually_maintained/cohere_aws/__pycache__/classification.cpython-39.pyc,,
cohere/manually_maintained/cohere_aws/__pycache__/client.cpython-39.pyc,,
cohere/manually_maintained/cohere_aws/__pycache__/embeddings.cpython-39.pyc,,
cohere/manually_maintained/cohere_aws/__pycache__/error.cpython-39.pyc,,
cohere/manually_maintained/cohere_aws/__pycache__/generation.cpython-39.pyc,,
cohere/manually_maintained/cohere_aws/__pycache__/mode.cpython-39.pyc,,
cohere/manually_maintained/cohere_aws/__pycache__/rerank.cpython-39.pyc,,
cohere/manually_maintained/cohere_aws/__pycache__/response.cpython-39.pyc,,
cohere/manually_maintained/cohere_aws/__pycache__/summary.cpython-39.pyc,,
cohere/manually_maintained/cohere_aws/chat.py,sha256=yo1W4DsaI6BlhD8sMZ2B5FOadTnvse_Vz2lxr4RYWE8,11351
cohere/manually_maintained/cohere_aws/classification.py,sha256=Ag7M9sshichQPXSXcaqASTj_F1BWJOSCs-RZRxb_3OQ,2461
cohere/manually_maintained/cohere_aws/client.py,sha256=9ZoP8iyuE1aWQ8NMIu-D5kJNzruW_2VECsJ6d602Z7w,43799
cohere/manually_maintained/cohere_aws/embeddings.py,sha256=FAA8bSnvGAcoovfc6kAa1LfE7wYKS3xz9k3m0FZPWy0,607
cohere/manually_maintained/cohere_aws/error.py,sha256=rmn_cVKMt0Fn0oCuI7ZBC9DYYZJUWVvm_t3m2By3zIE,591
cohere/manually_maintained/cohere_aws/generation.py,sha256=yp6gW1OtKrNEnHZm9xRSF8BhI4BelnppXiJz_p5t60U,3611
cohere/manually_maintained/cohere_aws/mode.py,sha256=18-SzAY9dnT2FEorjdWzUVpticXVCshPXCPdkj-DC-w,76
cohere/manually_maintained/cohere_aws/rerank.py,sha256=NGAtkeaQvcleVjJgMbVXL84L_p0kEZl90ZmzO5IObZs,2195
cohere/manually_maintained/cohere_aws/response.py,sha256=CQglq-rOxbZOWeCXIveHRXZXF9gj9oE8DdAKnQnZV_c,337
cohere/manually_maintained/cohere_aws/summary.py,sha256=AMPnGZBKl-0jWj-XN3pmtLUR3uHjT3YF1AfOqGYbIkc,459
cohere/manually_maintained/lazy_aws_deps.py,sha256=eixFjCTCppSLGEFIfunHLdPyw-Ty_dfS_XM4_14SVpQ,557
cohere/manually_maintained/tokenizers.py,sha256=IHqfMtjzGS3DeRSci8VLQKGIsZP7-UYjiq5-FGQG77Y,3834
cohere/models/__init__.py,sha256=_VhToAyIt_5axN6CLJwtxg3-CO7THa_23pbUzqhXJa4,85
cohere/models/__pycache__/__init__.cpython-39.pyc,,
cohere/models/__pycache__/client.cpython-39.pyc,,
cohere/models/__pycache__/raw_client.cpython-39.pyc,,
cohere/models/client.py,sha256=wPd4Tu0YqUusoMzdetvnfTqCjmuSarq0mPSrJ3lmVkg,6872
cohere/models/raw_client.py,sha256=PzX7_ytMKQuX8tJ1Brk700SfaPiN-pShs9dfZhx4bE4,32121
cohere/overrides.py,sha256=impnWDl94f8avpsFHawrso3uONoJmBRE0MZpwdM5tR0,1615
cohere/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
cohere/raw_base_client.py,sha256=oaFUB0vN3R5oXCK4SmNkUyI8J4pEfiffuASRHuvTNro,290842
cohere/sagemaker_client.py,sha256=REYOXh7R0ggN5HlokOHK-JMYSkY0EalUYXfai7s3evA,1821
cohere/types/__init__.py,sha256=BRUcLqJsPDus7rDUlIIFwsOudBKMBa3NaW64ZHTAzyw,18948
cohere/types/__pycache__/__init__.cpython-39.pyc,,
cohere/types/__pycache__/api_meta.cpython-39.pyc,,
cohere/types/__pycache__/api_meta_api_version.cpython-39.pyc,,
cohere/types/__pycache__/api_meta_billed_units.cpython-39.pyc,,
cohere/types/__pycache__/api_meta_tokens.cpython-39.pyc,,
cohere/types/__pycache__/assistant_message.cpython-39.pyc,,
cohere/types/__pycache__/assistant_message_response.cpython-39.pyc,,
cohere/types/__pycache__/assistant_message_response_content_item.cpython-39.pyc,,
cohere/types/__pycache__/assistant_message_v2content.cpython-39.pyc,,
cohere/types/__pycache__/assistant_message_v2content_item.cpython-39.pyc,,
cohere/types/__pycache__/auth_token_type.cpython-39.pyc,,
cohere/types/__pycache__/chat_citation.cpython-39.pyc,,
cohere/types/__pycache__/chat_citation_generation_event.cpython-39.pyc,,
cohere/types/__pycache__/chat_citation_type.cpython-39.pyc,,
cohere/types/__pycache__/chat_connector.cpython-39.pyc,,
cohere/types/__pycache__/chat_content_delta_event.cpython-39.pyc,,
cohere/types/__pycache__/chat_content_delta_event_delta.cpython-39.pyc,,
cohere/types/__pycache__/chat_content_delta_event_delta_message.cpython-39.pyc,,
cohere/types/__pycache__/chat_content_delta_event_delta_message_content.cpython-39.pyc,,
cohere/types/__pycache__/chat_content_end_event.cpython-39.pyc,,
cohere/types/__pycache__/chat_content_start_event.cpython-39.pyc,,
cohere/types/__pycache__/chat_content_start_event_delta.cpython-39.pyc,,
cohere/types/__pycache__/chat_content_start_event_delta_message.cpython-39.pyc,,
cohere/types/__pycache__/chat_content_start_event_delta_message_content.cpython-39.pyc,,
cohere/types/__pycache__/chat_content_start_event_delta_message_content_type.cpython-39.pyc,,
cohere/types/__pycache__/chat_data_metrics.cpython-39.pyc,,
cohere/types/__pycache__/chat_debug_event.cpython-39.pyc,,
cohere/types/__pycache__/chat_document.cpython-39.pyc,,
cohere/types/__pycache__/chat_document_source.cpython-39.pyc,,
cohere/types/__pycache__/chat_finish_reason.cpython-39.pyc,,
cohere/types/__pycache__/chat_message.cpython-39.pyc,,
cohere/types/__pycache__/chat_message_end_event.cpython-39.pyc,,
cohere/types/__pycache__/chat_message_end_event_delta.cpython-39.pyc,,
cohere/types/__pycache__/chat_message_start_event.cpython-39.pyc,,
cohere/types/__pycache__/chat_message_start_event_delta.cpython-39.pyc,,
cohere/types/__pycache__/chat_message_start_event_delta_message.cpython-39.pyc,,
cohere/types/__pycache__/chat_message_v2.cpython-39.pyc,,
cohere/types/__pycache__/chat_messages.cpython-39.pyc,,
cohere/types/__pycache__/chat_request_citation_quality.cpython-39.pyc,,
cohere/types/__pycache__/chat_request_prompt_truncation.cpython-39.pyc,,
cohere/types/__pycache__/chat_request_safety_mode.cpython-39.pyc,,
cohere/types/__pycache__/chat_search_queries_generation_event.cpython-39.pyc,,
cohere/types/__pycache__/chat_search_query.cpython-39.pyc,,
cohere/types/__pycache__/chat_search_result.cpython-39.pyc,,
cohere/types/__pycache__/chat_search_result_connector.cpython-39.pyc,,
cohere/types/__pycache__/chat_search_results_event.cpython-39.pyc,,
cohere/types/__pycache__/chat_stream_end_event.cpython-39.pyc,,
cohere/types/__pycache__/chat_stream_end_event_finish_reason.cpython-39.pyc,,
cohere/types/__pycache__/chat_stream_event.cpython-39.pyc,,
cohere/types/__pycache__/chat_stream_event_type.cpython-39.pyc,,
cohere/types/__pycache__/chat_stream_request_citation_quality.cpython-39.pyc,,
cohere/types/__pycache__/chat_stream_request_prompt_truncation.cpython-39.pyc,,
cohere/types/__pycache__/chat_stream_request_safety_mode.cpython-39.pyc,,
cohere/types/__pycache__/chat_stream_start_event.cpython-39.pyc,,
cohere/types/__pycache__/chat_text_content.cpython-39.pyc,,
cohere/types/__pycache__/chat_text_generation_event.cpython-39.pyc,,
cohere/types/__pycache__/chat_text_response_format.cpython-39.pyc,,
cohere/types/__pycache__/chat_text_response_format_v2.cpython-39.pyc,,
cohere/types/__pycache__/chat_tool_call_delta_event.cpython-39.pyc,,
cohere/types/__pycache__/chat_tool_call_delta_event_delta.cpython-39.pyc,,
cohere/types/__pycache__/chat_tool_call_delta_event_delta_message.cpython-39.pyc,,
cohere/types/__pycache__/chat_tool_call_delta_event_delta_message_tool_calls.cpython-39.pyc,,
cohere/types/__pycache__/chat_tool_call_delta_event_delta_message_tool_calls_function.cpython-39.pyc,,
cohere/types/__pycache__/chat_tool_call_end_event.cpython-39.pyc,,
cohere/types/__pycache__/chat_tool_call_start_event.cpython-39.pyc,,
cohere/types/__pycache__/chat_tool_call_start_event_delta.cpython-39.pyc,,
cohere/types/__pycache__/chat_tool_call_start_event_delta_message.cpython-39.pyc,,
cohere/types/__pycache__/chat_tool_calls_chunk_event.cpython-39.pyc,,
cohere/types/__pycache__/chat_tool_calls_generation_event.cpython-39.pyc,,
cohere/types/__pycache__/chat_tool_message.cpython-39.pyc,,
cohere/types/__pycache__/chat_tool_plan_delta_event.cpython-39.pyc,,
cohere/types/__pycache__/chat_tool_plan_delta_event_delta.cpython-39.pyc,,
cohere/types/__pycache__/chat_tool_plan_delta_event_delta_message.cpython-39.pyc,,
cohere/types/__pycache__/chat_tool_source.cpython-39.pyc,,
cohere/types/__pycache__/check_api_key_response.cpython-39.pyc,,
cohere/types/__pycache__/citation.cpython-39.pyc,,
cohere/types/__pycache__/citation_end_event.cpython-39.pyc,,
cohere/types/__pycache__/citation_options.cpython-39.pyc,,
cohere/types/__pycache__/citation_options_mode.cpython-39.pyc,,
cohere/types/__pycache__/citation_start_event.cpython-39.pyc,,
cohere/types/__pycache__/citation_start_event_delta.cpython-39.pyc,,
cohere/types/__pycache__/citation_start_event_delta_message.cpython-39.pyc,,
cohere/types/__pycache__/citation_type.cpython-39.pyc,,
cohere/types/__pycache__/classify_data_metrics.cpython-39.pyc,,
cohere/types/__pycache__/classify_example.cpython-39.pyc,,
cohere/types/__pycache__/classify_request_truncate.cpython-39.pyc,,
cohere/types/__pycache__/classify_response.cpython-39.pyc,,
cohere/types/__pycache__/classify_response_classifications_item.cpython-39.pyc,,
cohere/types/__pycache__/classify_response_classifications_item_classification_type.cpython-39.pyc,,
cohere/types/__pycache__/classify_response_classifications_item_labels_value.cpython-39.pyc,,
cohere/types/__pycache__/compatible_endpoint.cpython-39.pyc,,
cohere/types/__pycache__/connector.cpython-39.pyc,,
cohere/types/__pycache__/connector_auth_status.cpython-39.pyc,,
cohere/types/__pycache__/connector_o_auth.cpython-39.pyc,,
cohere/types/__pycache__/content.cpython-39.pyc,,
cohere/types/__pycache__/create_connector_o_auth.cpython-39.pyc,,
cohere/types/__pycache__/create_connector_response.cpython-39.pyc,,
cohere/types/__pycache__/create_connector_service_auth.cpython-39.pyc,,
cohere/types/__pycache__/create_embed_job_response.cpython-39.pyc,,
cohere/types/__pycache__/dataset.cpython-39.pyc,,
cohere/types/__pycache__/dataset_part.cpython-39.pyc,,
cohere/types/__pycache__/dataset_type.cpython-39.pyc,,
cohere/types/__pycache__/dataset_validation_status.cpython-39.pyc,,
cohere/types/__pycache__/delete_connector_response.cpython-39.pyc,,
cohere/types/__pycache__/detokenize_response.cpython-39.pyc,,
cohere/types/__pycache__/document.cpython-39.pyc,,
cohere/types/__pycache__/document_content.cpython-39.pyc,,
cohere/types/__pycache__/embed_by_type_response.cpython-39.pyc,,
cohere/types/__pycache__/embed_by_type_response_embeddings.cpython-39.pyc,,
cohere/types/__pycache__/embed_content.cpython-39.pyc,,
cohere/types/__pycache__/embed_floats_response.cpython-39.pyc,,
cohere/types/__pycache__/embed_image.cpython-39.pyc,,
cohere/types/__pycache__/embed_image_url.cpython-39.pyc,,
cohere/types/__pycache__/embed_input.cpython-39.pyc,,
cohere/types/__pycache__/embed_input_type.cpython-39.pyc,,
cohere/types/__pycache__/embed_job.cpython-39.pyc,,
cohere/types/__pycache__/embed_job_status.cpython-39.pyc,,
cohere/types/__pycache__/embed_job_truncate.cpython-39.pyc,,
cohere/types/__pycache__/embed_request_truncate.cpython-39.pyc,,
cohere/types/__pycache__/embed_response.cpython-39.pyc,,
cohere/types/__pycache__/embed_text.cpython-39.pyc,,
cohere/types/__pycache__/embedding_type.cpython-39.pyc,,
cohere/types/__pycache__/finetune_dataset_metrics.cpython-39.pyc,,
cohere/types/__pycache__/finish_reason.cpython-39.pyc,,
cohere/types/__pycache__/generate_request_return_likelihoods.cpython-39.pyc,,
cohere/types/__pycache__/generate_request_truncate.cpython-39.pyc,,
cohere/types/__pycache__/generate_stream_end.cpython-39.pyc,,
cohere/types/__pycache__/generate_stream_end_response.cpython-39.pyc,,
cohere/types/__pycache__/generate_stream_error.cpython-39.pyc,,
cohere/types/__pycache__/generate_stream_event.cpython-39.pyc,,
cohere/types/__pycache__/generate_stream_request_return_likelihoods.cpython-39.pyc,,
cohere/types/__pycache__/generate_stream_request_truncate.cpython-39.pyc,,
cohere/types/__pycache__/generate_stream_text.cpython-39.pyc,,
cohere/types/__pycache__/generate_streamed_response.cpython-39.pyc,,
cohere/types/__pycache__/generation.cpython-39.pyc,,
cohere/types/__pycache__/get_connector_response.cpython-39.pyc,,
cohere/types/__pycache__/get_model_response.cpython-39.pyc,,
cohere/types/__pycache__/image.cpython-39.pyc,,
cohere/types/__pycache__/image_content.cpython-39.pyc,,
cohere/types/__pycache__/image_url.cpython-39.pyc,,
cohere/types/__pycache__/image_url_detail.cpython-39.pyc,,
cohere/types/__pycache__/json_response_format.cpython-39.pyc,,
cohere/types/__pycache__/json_response_format_v2.cpython-39.pyc,,
cohere/types/__pycache__/label_metric.cpython-39.pyc,,
cohere/types/__pycache__/list_connectors_response.cpython-39.pyc,,
cohere/types/__pycache__/list_embed_job_response.cpython-39.pyc,,
cohere/types/__pycache__/list_models_response.cpython-39.pyc,,
cohere/types/__pycache__/logprob_item.cpython-39.pyc,,
cohere/types/__pycache__/message.cpython-39.pyc,,
cohere/types/__pycache__/metrics.cpython-39.pyc,,
cohere/types/__pycache__/non_streamed_chat_response.cpython-39.pyc,,
cohere/types/__pycache__/o_auth_authorize_response.cpython-39.pyc,,
cohere/types/__pycache__/parse_info.cpython-39.pyc,,
cohere/types/__pycache__/rerank_document.cpython-39.pyc,,
cohere/types/__pycache__/rerank_request_documents_item.cpython-39.pyc,,
cohere/types/__pycache__/rerank_response.cpython-39.pyc,,
cohere/types/__pycache__/rerank_response_results_item.cpython-39.pyc,,
cohere/types/__pycache__/rerank_response_results_item_document.cpython-39.pyc,,
cohere/types/__pycache__/reranker_data_metrics.cpython-39.pyc,,
cohere/types/__pycache__/response_format.cpython-39.pyc,,
cohere/types/__pycache__/response_format_v2.cpython-39.pyc,,
cohere/types/__pycache__/single_generation.cpython-39.pyc,,
cohere/types/__pycache__/single_generation_in_stream.cpython-39.pyc,,
cohere/types/__pycache__/single_generation_token_likelihoods_item.cpython-39.pyc,,
cohere/types/__pycache__/source.cpython-39.pyc,,
cohere/types/__pycache__/streamed_chat_response.cpython-39.pyc,,
cohere/types/__pycache__/summarize_request_extractiveness.cpython-39.pyc,,
cohere/types/__pycache__/summarize_request_format.cpython-39.pyc,,
cohere/types/__pycache__/summarize_request_length.cpython-39.pyc,,
cohere/types/__pycache__/summarize_response.cpython-39.pyc,,
cohere/types/__pycache__/system_message_v2.cpython-39.pyc,,
cohere/types/__pycache__/system_message_v2content.cpython-39.pyc,,
cohere/types/__pycache__/system_message_v2content_item.cpython-39.pyc,,
cohere/types/__pycache__/thinking.cpython-39.pyc,,
cohere/types/__pycache__/thinking_type.cpython-39.pyc,,
cohere/types/__pycache__/tokenize_response.cpython-39.pyc,,
cohere/types/__pycache__/tool.cpython-39.pyc,,
cohere/types/__pycache__/tool_call.cpython-39.pyc,,
cohere/types/__pycache__/tool_call_delta.cpython-39.pyc,,
cohere/types/__pycache__/tool_call_v2.cpython-39.pyc,,
cohere/types/__pycache__/tool_call_v2function.cpython-39.pyc,,
cohere/types/__pycache__/tool_content.cpython-39.pyc,,
cohere/types/__pycache__/tool_message_v2.cpython-39.pyc,,
cohere/types/__pycache__/tool_message_v2content.cpython-39.pyc,,
cohere/types/__pycache__/tool_parameter_definitions_value.cpython-39.pyc,,
cohere/types/__pycache__/tool_result.cpython-39.pyc,,
cohere/types/__pycache__/tool_v2.cpython-39.pyc,,
cohere/types/__pycache__/tool_v2function.cpython-39.pyc,,
cohere/types/__pycache__/update_connector_response.cpython-39.pyc,,
cohere/types/__pycache__/usage.cpython-39.pyc,,
cohere/types/__pycache__/usage_billed_units.cpython-39.pyc,,
cohere/types/__pycache__/usage_tokens.cpython-39.pyc,,
cohere/types/__pycache__/user_message_v2.cpython-39.pyc,,
cohere/types/__pycache__/user_message_v2content.cpython-39.pyc,,
cohere/types/api_meta.py,sha256=vE5vuPhJuELq21V5EI88vJrQ81-N1sWMjz1H6AyojBI,874
cohere/types/api_meta_api_version.py,sha256=0ZVkdzmoLz4H8nDXfkhrFSi-Jv5Itt1WzNmlAEKj7vs,625
cohere/types/api_meta_billed_units.py,sha256=rYE3Ojh7zRTcFto9zT14_M5H6jIDlHP0D_rgIUhjSe0,1152
cohere/types/api_meta_tokens.py,sha256=l261JTOaUa_QGnLn5hqym2qpZ94ubM8I0X1Zb4fzmSg,785
cohere/types/assistant_message.py,sha256=fwhVInECK1Ab-d5QFfZ2VZd38SMHyoZ7yEYIX-u5P1c,1114
cohere/types/assistant_message_response.py,sha256=qWLMF8n1B5cZVRfdq594018hXYDByoaRKxjX2JtsC_w,1219
cohere/types/assistant_message_response_content_item.py,sha256=_v0Nkj51W8H2KKba57Ug0gmkjMYOLmOhvD9vaVSwTXA,1130
cohere/types/assistant_message_v2content.py,sha256=WWR0jB0Tb85GVCMK6BNxVgSiCYbe5OWIuE2UMDmADsI,247
cohere/types/assistant_message_v2content_item.py,sha256=e3r4OMAXrDxIVijb5fnFhfDKBA7DeQA3gpt_ORETVA4,1100
cohere/types/auth_token_type.py,sha256=AQmH1BCKJgW6rMn5d0mlcD6itpVzi1Wtyw0WctK1Etw,168
cohere/types/chat_citation.py,sha256=FX2C_gwzaj2dsavnXFvCfxDQ_xZmSJSpYE9VT5PZpyg,1760
cohere/types/chat_citation_generation_event.py,sha256=AY3CkhuPO5HNhcya0JWCiAP7WKBXENUI1_jOGVVi2f0,660
cohere/types/chat_citation_type.py,sha256=JEX2_1y1OPx3osOJ06dtFsDI9ROSu2n1tACa2DFxq1s,164
cohere/types/chat_connector.py,sha256=n_6zAC6bQRersMu9D_wA8YY9zNUPFHY68gFAblVgoYM,1480
cohere/types/chat_content_delta_event.py,sha256=f7qwl0LZiejGgaUmHIuo0iyJAUWYm0oALop-wRK7aXk,861
cohere/types/chat_content_delta_event_delta.py,sha256=eElS8hdSXUCT2HPkYHg__5fSjNcf2Jc7wgIqspJ__9Q,676
cohere/types/chat_content_delta_event_delta_message.py,sha256=5J16KqTx1-0oyWbf0YSoktRDR0gSD7tfYsf0IxssxI0,705
cohere/types/chat_content_delta_event_delta_message_content.py,sha256=n52iWkGGlSdvnnWIU5P4YnI3M2pte4w5D1JBbsdvAnU,613
cohere/types/chat_content_end_event.py,sha256=b3gsbXFOZ3AJf15khQzUADkb3LAv1dkq9ue1qiezWyY,643
cohere/types/chat_content_start_event.py,sha256=OOu5brB5uJ6vAJLwcmWqSP-pQ98sOxtvG3STB0CGs3M,782
cohere/types/chat_content_start_event_delta.py,sha256=vmgHyUndKQz-pAemNRE1o3-hwyoUi4dIvq0k16Ii6_g,676
cohere/types/chat_content_start_event_delta_message.py,sha256=elhRci7YhhcCVopHycKSE-7oWyI3itdZ4YCgY24VbJo,705
cohere/types/chat_content_start_event_delta_message_content.py,sha256=qBEZYAMPhsyhlAS-86NDWSbuAA9Vh7VHBGPavVxFwoM,760
cohere/types/chat_content_start_event_delta_message_content_type.py,sha256=7vL5D5lfPLc4DPc_w8PY66DwMB6qOng18GQXuZ-jKXA,188
cohere/types/chat_data_metrics.py,sha256=Hcjdeb8BdupsVtyfhHwGZWHiX4Acju88zL3NrYJ3Yn8,902
cohere/types/chat_debug_event.py,sha256=LWjBSoh_YnNSJ8O2gOJoIEOV5OSnIdhtMbjLrsMH7zc,532
cohere/types/chat_document.py,sha256=AZqukupDQO9n3RXbJotqDXyYZwBW6kBpFS_ULkqXDB0,117
cohere/types/chat_document_source.py,sha256=IEnx2xsGhqu3SA7ozATu6atR2Z6z7mRvzVF601nzVIw,834
cohere/types/chat_finish_reason.py,sha256=dEqMZxmICcUjXrTcLivZifNoXNc6b6m1c_Nf7WMrSto,211
cohere/types/chat_message.py,sha256=p87pJAkqeFy_a7J9M4MB9tMGk6l_CsLk6BW7EiFaq-k,1152
cohere/types/chat_message_end_event.py,sha256=4x1HWkJAyVPLlUhpDP47FtGy0Dqq8phknVBhp04T1zs,760
cohere/types/chat_message_end_event_delta.py,sha256=k0YCgVL-NwugLIGEb_ALtVQoMxRrqYDE04SY54cn93Q,837
cohere/types/chat_message_start_event.py,sha256=MnIQ090hb2fPORhGX741GrAkkKIayT6QUHsti8Puuwg,850
cohere/types/chat_message_start_event_delta.py,sha256=yxFkpby2hr48wH57_NeA-f9PoGJvF86B7sCgyv8ipIc,676
cohere/types/chat_message_start_event_delta_message.py,sha256=1YJB2J6B8vXb59RHt7n2TeuaBXsOBvXgzSsvJcaKQO0,657
cohere/types/chat_message_v2.py,sha256=YL36_ZppcY--WFDmSOkkihFYXC-qNytYwebovOqOZyY,2890
cohere/types/chat_messages.py,sha256=-6zBzlggl__c6zrW3KEVVawLbYqZB1TVIqyCqNMA5mw,166
cohere/types/chat_request_citation_quality.py,sha256=tufVRDnY3jcgomhe2IpmXTr0JGMa-hMaJfcq2EOv3TY,177
cohere/types/chat_request_prompt_truncation.py,sha256=JZscyWGgcQxtHl5Y2sikD6buxx_DiBhGevqe36uC-Wo,189
cohere/types/chat_request_safety_mode.py,sha256=gTJbm52fAA48t8L9ibWgQHacybl5S9xBjpZ8LAhQYno,177
cohere/types/chat_search_queries_generation_event.py,sha256=Eyv-n58K4Mv80zDekvvUrwIY-t-HNcVflKCEkX7M3BM,713
cohere/types/chat_search_query.py,sha256=8y4RaSc-0vHtclpC004MllVHdr2TmmKwQKciYSQc4CI,855
cohere/types/chat_search_result.py,sha256=tKudAg2uEmaqWKcihMCRrVX7864yAoZbp5GqbaT5Gk8,1255
cohere/types/chat_search_result_connector.py,sha256=yE8lvdE_XrbTMm1qC2WlrHkQoeq82vQ-V9jMnQijmwA,666
cohere/types/chat_search_results_event.py,sha256=D4oM1BukA2-VHAuOGoVqbYyDlorZQjbq0X5hoX8pQFU,947
cohere/types/chat_stream_end_event.py,sha256=vHmDCq2IugQwAZU83OpYapFtX3qAU0TQ3JzNx7mnaUo,1391
cohere/types/chat_stream_end_event_finish_reason.py,sha256=DU32GwkalGiEIBg6CnMY0dJZOV7W3AovkIXp40eW7pQ,225
cohere/types/chat_stream_event.py,sha256=uSCgD9zHL-8Ba_rGOBEZVozhbS6C2A22YM9WcqJoYk4,507
cohere/types/chat_stream_event_type.py,sha256=bM5Pef2RCgTdBjddgyCoz5UzWfugRflFYcUont3hJjc,557
cohere/types/chat_stream_request_citation_quality.py,sha256=-mqj4msWULs8fO8J7NvEy7PeAlFcxfvwdpA1lFMky6A,183
cohere/types/chat_stream_request_prompt_truncation.py,sha256=Kwtx_ZWymZyMjJGISXqyd6nryqslQtSjqufxKc_YAys,195
cohere/types/chat_stream_request_safety_mode.py,sha256=UwK-L9YxVpDpjj8lNn3UiEp_6AiFhrR114IrwMlxyE8,183
cohere/types/chat_stream_start_event.py,sha256=9mQJra8UHXIbfJ0c6_K3enQ4q7wKdZZh8YfASFqdmZo,635
cohere/types/chat_text_content.py,sha256=GQd3pnS22Xc8DAUDc3xKdcwZxQdOntuWg9O22tIYeE8,572
cohere/types/chat_text_generation_event.py,sha256=7IjpxjW6RpAUuJMTXiCWl3pG_HW-F_WfrAZpcXAY-8M,601
cohere/types/chat_text_response_format.py,sha256=xmH5Bfnwa9wQzwr8wnK-DqH8Iaqtco_BicQ9mjvAPkE,514
cohere/types/chat_text_response_format_v2.py,sha256=QirstakFajwLUjyp-FRC9fCz_xbRF1-Z1tW76ap4YAE,516
cohere/types/chat_tool_call_delta_event.py,sha256=b7nzoEDNy0rWclSp4C3Oa6CNIBOvkSrAjFpJ4d_TLWw,781
cohere/types/chat_tool_call_delta_event_delta.py,sha256=Y7QYtz8V-wUQQAzxfs9q1qyUs160LD6zbzanHyKz4Jg,681
cohere/types/chat_tool_call_delta_event_delta_message.py,sha256=CbGq0eJtfy4LbhSF8sy67u2vyisnJCsH8z5TA5kjuWM,720
cohere/types/chat_tool_call_delta_event_delta_message_tool_calls.py,sha256=HIfNZePAqsxXXdtg3yHl7jXTolyq7h1rSyeSQIYHLFk,761
cohere/types/chat_tool_call_delta_event_delta_message_tool_calls_function.py,sha256=IQHlKRVmqQwm-tfWDh9IfP1bRdVYxEIvuFoSr7euAN8,587
cohere/types/chat_tool_call_end_event.py,sha256=JC-uxi4gVePd2srvccNpsQlIp--UBsG7gW0byRM9nQg,646
cohere/types/chat_tool_call_start_event.py,sha256=Ot1rTYR85pOt23KDIVZ0YAGVRiH_RWT2JbaOypUue6A,784
cohere/types/chat_tool_call_start_event_delta.py,sha256=U6eu_cQRpohBPqy9iiaaR7EzyaUe_90bkntWE4hNFIE,681
cohere/types/chat_tool_call_start_event_delta_message.py,sha256=QZ82GGkQFfIg-dNTLVJFSzY85l1FJaCYuHHEbDRiu-I,615
cohere/types/chat_tool_calls_chunk_event.py,sha256=uxrs4_MIA_34pzm_znrAZP-3H-kbx7U7rhfvSsHTqTI,617
cohere/types/chat_tool_calls_generation_event.py,sha256=NlgnCL1lxoEJl3socIPevR05cjKIGg-L88eHrs2y0F0,714
cohere/types/chat_tool_message.py,sha256=NyJp_B22_uJExApzgCsn_G4tZBC4VmJ9ohOpjCpw8mI,675
cohere/types/chat_tool_plan_delta_event.py,sha256=4m9XsWTBOotl3fg2k3md6N2a7MAWVCZxD6k7u97RsME,730
cohere/types/chat_tool_plan_delta_event_delta.py,sha256=S1dXxUyg1DaHKfMfeLbWmpjlhQWZ24xyDdp_COQHfz8,681
cohere/types/chat_tool_plan_delta_event_delta_message.py,sha256=08k2LEJS9bMQ469ureRp7ZTZljK8vFOldFYrnGzFj7E,570
cohere/types/chat_tool_source.py,sha256=P177XCQ4Bx3Ba-SzPq0wYn9w_h6hzr_2vxXLsI8SfDQ,713
cohere/types/check_api_key_response.py,sha256=RIjrVHEZjubZafG_aYuKbGQdTbbNx4C02V7uyxgdrU0,619
cohere/types/citation.py,sha256=7RTHNQzgqxrrxIuhRrAWS-cQaH4yJS54dMpFRxdW73g,1309
cohere/types/citation_end_event.py,sha256=hl4jMitHVHEnDduKjU1OnJKlUNnPwAr-KDpmd4ysH4I,635
cohere/types/citation_options.py,sha256=f9VCI0E-If8JrsaR-tXF8i261xXlh4dkmgyWRh8w08U,1068
cohere/types/citation_options_mode.py,sha256=fCe-Wb87TBndIP023VaLZId_ZvyF0ioBGEv1P7tNYec,170
cohere/types/citation_start_event.py,sha256=1qDMjfbWcd5JbSsIgnk8SsEEvsrOqjhg027x07KlHL0,754
cohere/types/citation_start_event_delta.py,sha256=bw7cBnRVw-a5Yu9uAH_dzL1MxqPqDLQNLfYofz0ucvM,663
cohere/types/citation_start_event_delta_message.py,sha256=KqcJIu6FE50ApoyBopEdVMtcvjAQbtfOcNvhgQ8iIgI,602
cohere/types/citation_type.py,sha256=-pjoBHkuDxX4ktMZ9_AHud9--bxr4Vr-0JtOfwuL6fY,180
cohere/types/classify_data_metrics.py,sha256=R0_g2Djt-vsYLAIsUgWO3YIRos07LqLayva-lnQF--E,618
cohere/types/classify_example.py,sha256=dl199ktHf3BZAHATtTfEC16uXp1XtO4U1ZOsptuFXmo,585
cohere/types/classify_request_truncate.py,sha256=TKdXn82b_64yxc26GBP3Ry23QscuKxyXVVw6oil8BT4,171
cohere/types/classify_response.py,sha256=YiPQj0o75eR157FvGki9E5g1UzRw3ADLStS_KE4iZ1A,751
cohere/types/classify_response_classifications_item.py,sha256=ErT16T_tagb3Lf3T3IAUbZowwyEglASxAArL1ZLSSuI,2153
cohere/types/classify_response_classifications_item_classification_type.py,sha256=uxDQgMTHLjQ5OhvAhuLs75AgWQOUIerqUbHC-RrA2UA,214
cohere/types/classify_response_classifications_item_labels_value.py,sha256=sPy6rpxXs8uRw5enIOo3p0pb8A_NkuDaiAqjGb-DCaU,585
cohere/types/compatible_endpoint.py,sha256=OQyyMpzT8s02WuQvLprJUHxRsHVp_AiJKZLJsghgTbA,220
cohere/types/connector.py,sha256=VHGfrDWeZiDXNmRuLbbnB-SI8Jid_Fzc7fJu6ob1hzY,2980
cohere/types/connector_auth_status.py,sha256=tdWndU6GSLTBfsXEfBAJ4d2Yytw3Hn_OtvrK7uo_w4g,163
cohere/types/connector_o_auth.py,sha256=75Mb-kX1fcEnDfIw7QzZD_z6ATBzoGnaOYOYZbTJnaQ,1259
cohere/types/content.py,sha256=5SbrxmxhvbDWMIuo3HQLVl44nFtNscOpsysDTEPRevg,1386
cohere/types/create_connector_o_auth.py,sha256=fkLZIuMoNPRj_hOuUIMYSNXluC8dmYIbC49YOjWfTbE,1324
cohere/types/create_connector_response.py,sha256=PBk--BnlPM6_4TVxTRJGukq9baJ9M8_0PwSYQg86Dl4,574
cohere/types/create_connector_service_auth.py,sha256=RHMmEoh5YoZoHkIFpRpPdqPExnPeHDC07itoNjXTqHE,806
cohere/types/create_embed_job_response.py,sha256=403uZWv_qwqrmXaDHBFAuvPQSRiqHYQukx3dCyhHhWc,661
cohere/types/dataset.py,sha256=exwiLgR1E4vxqtFIpDdbC8nqPyEcLhB8R12cdy-Gst0,1873
cohere/types/dataset_part.py,sha256=KtJekUr5E5_M1XTAOr8NdNb7Mtxm4A74LskMKY626uQ,1378
cohere/types/dataset_type.py,sha256=fRR-olDI-avZFb_iHehRod25ipAT_7v_rjSzHil8v5U,427
cohere/types/dataset_validation_status.py,sha256=FVn6BzIrZ_vtnc7A9-S5SBCrgWgJdfPSSEF5EMCf0RU,222
cohere/types/delete_connector_response.py,sha256=R-z_OGKebOC_Q3MKxLx5FcxdgsXPsMtrmRRQwJaVCX4,152
cohere/types/detokenize_response.py,sha256=WZaUyntSxsoxNnwDdRF0QkW6G5FJ5ZoksVbbuvVMPnA,679
cohere/types/document.py,sha256=GP4WX7v47i7PWAz_lSx_ipxXsKxSDEvNm04lo3kyr0U,1243
cohere/types/document_content.py,sha256=pFih8xC1CfDUOSqy2ygbVGiE3sd5_t3DRhhPQ_rlvzw,601
cohere/types/embed_by_type_response.py,sha256=jgU_CDa8Xl_86U5MKjVIxfDy3XDHENBKMFRvoNQ-Nqo,1232
cohere/types/embed_by_type_response_embeddings.py,sha256=gnaEJ_3KqnzY8JoqfFJyLaBXph42Jj37IVH4gyqYDz8,1922
cohere/types/embed_content.py,sha256=f1Vj8yxTG4YMUkiFWBnuNdqvb_OXVzSufdiKzx5oQiY,1259
cohere/types/embed_floats_response.py,sha256=mMV1iANygfS1jI52Dpk2-qTx8e1sB5U0i4oxJCt-dR4,1151
cohere/types/embed_image.py,sha256=B3qYE1l4lpMVweqh6qZL2RMXZRxRjJmq3fY903O5dtQ,692
cohere/types/embed_image_url.py,sha256=X3TU9NCrQGy5D6w9RGxqO9bBIzibGRXqJLPHk5YClxM,561
cohere/types/embed_input.py,sha256=jdF_7LZO-wXOHlqWYVuvSjmEAJbf0gRoMy9HAABPjzs,691
cohere/types/embed_input_type.py,sha256=Qc7TqKbE-qejyk_RlGG8EyZ5aM2K3hFjcbBnAzkRwwA,220
cohere/types/embed_job.py,sha256=5IJu93KfDL45MX8tiNp46W4xJS4xStcpql1rf60P_xo,1476
cohere/types/embed_job_status.py,sha256=W6dPm5AFkNf-ZffDDHpINzQFTJg8LV7FXVT6zf4Lyvo,201
cohere/types/embed_job_truncate.py,sha256=8sv4eggpQ7mPpFHheTGaFXIriRN-8ATz5rSYHLYEwSk,156
cohere/types/embed_request_truncate.py,sha256=tBf20S3DnyKvxotAe9WhpAi-dHGUkcrJbq4DkoZGIBw,168
cohere/types/embed_response.py,sha256=b5hdAFEeFe4fm-DGn-6DnP9p5yTXSmjiidmRAyjuMeU,1770
cohere/types/embed_text.py,sha256=cg0hw6JpWKE_vOZo1ZqlgPA65QxUnpDlbMdLxDlzvAM,588
cohere/types/embedding_type.py,sha256=x7kJ_13_tMtFjid1hEJn7JkQJz9Nma8cFaqeP3w2_b0,184
cohere/types/finetune_dataset_metrics.py,sha256=lnmVmQQjM1_qRnZahD_89s4QS2MckCt9GP_ytOZ_BUc,1331
cohere/types/finish_reason.py,sha256=s5eHrYoIReueONqZ2GjJ1kkuuaB5ylCk1l_2HAZiu3A,244
cohere/types/generate_request_return_likelihoods.py,sha256=ByEfuP4reLXZQm0cA77tFoLDxgnLf4aK-bRFfuS6ydQ,185
cohere/types/generate_request_truncate.py,sha256=fuXd-KHG3HRfORgUwt-DA4VLcoCzIFM1Bv7jWWwzCE8,171
cohere/types/generate_stream_end.py,sha256=m8ddec8tZjJO85uBH1SZOni0RBsX2lcbhNvUmKDkCv8,733
cohere/types/generate_stream_end_response.py,sha256=WYOiVkA-vvE-JZitWIUzlJoU7ADZDOs_VuVtb29iUr0,715
cohere/types/generate_stream_error.py,sha256=flfr_lv2QfqIOI_gb95yjdpEMJyzWagR8ZxjUI4hRfM,841
cohere/types/generate_stream_event.py,sha256=oqSx5xcWnD7WiXksdH-f1syXwrUzunk2s38uJM_JOI0,511
cohere/types/generate_stream_request_return_likelihoods.py,sha256=9DBmSUnXJNjkJXPDrF2hGy2N7q7dThRfCMenLcUfx-4,191
cohere/types/generate_stream_request_truncate.py,sha256=5cOP9rNzdtIhuAeNDYQt1-g6Ey2PJHKame90_11ohuU,177
cohere/types/generate_stream_text.py,sha256=GEANHtorfjG21XMjeekXlc_XB56YNp9Q57CLgkwlA6Y,842
cohere/types/generate_streamed_response.py,sha256=UaohdyEFOCmHo5ga2A2XWBPot3cydS_G1d5mvWIoc0E,2774
cohere/types/generation.py,sha256=xf5xdTIThWxAi2h6C5VwHYmE760IGoWAcpwhHSKDAuQ,862
cohere/types/get_connector_response.py,sha256=V8cnx8cB-LvYdUKbmhw8-x3Am2xZNwYLZRVjWgq5B-o,571
cohere/types/get_model_response.py,sha256=jc7qo4aHGOqy20FQHBznLFjpS7lbPFvTSmPSky3Px4w,2160
cohere/types/image.py,sha256=TQ7PyGoxNLoFwPHmo7qdMb4vXGZFdJ-xB92zUIKNmhE,825
cohere/types/image_content.py,sha256=k6KoEQkAlz4b-HOUswlmRKi8cHKUL9hjOMJw6QfCe4w,612
cohere/types/image_url.py,sha256=5k8CKaUAbuG3P_p-qpSxvPPzaXJ-09St-HCYSt-FVmA,1004
cohere/types/image_url_detail.py,sha256=YFT9wyf8hqeKhQjRWMv97y-fbU2DB-oCbU5BpUcHWVU,161
cohere/types/json_response_format.py,sha256=u5I_VQSG2ZOtT-rjTTgIFHMPapVOObYqUYSkgPO2_n0,1328
cohere/types/json_response_format_v2.py,sha256=No73KbfdA0U-_3dvLswHTO5F-cZHr_prsgIWgijh75w,1243
cohere/types/label_metric.py,sha256=ir_mjPQOc4zI36wgRg2ps7wWyem-n84M3w3gExOJMp0,861
cohere/types/list_connectors_response.py,sha256=3vc0SzLT6eJc8vUd8wJFD8haylELU7qjRx9CBSi5c4s,706
cohere/types/list_embed_job_response.py,sha256=qsc6LzmHDse6fxi2I3M6Iu3mdyvFtGHjCS1bvZhvgwg,607
cohere/types/list_models_response.py,sha256=a6UOM7mwhinB6Gi-TFGSNuXsuP5MsUxDVglwqY9MCfY,798
cohere/types/logprob_item.py,sha256=5y9zm8mnQ-lf9417cERPaDVgIr-xzAZV7yvT4O1Hk6s,953
cohere/types/message.py,sha256=OOpedfjR1-peTbFbYmyrvu9ljsoVgKGtBIqekLqOks8,2175
cohere/types/metrics.py,sha256=RoYLUeKbI5iyJ2fWEGD2I4gnMLZ02mDiaBqKOzg3uek,638
cohere/types/non_streamed_chat_response.py,sha256=D6WTr8SoTGjJBy9_40x-MdoFatn7ulkoBb2aL0ffp7c,2477
cohere/types/o_auth_authorize_response.py,sha256=tSKNsfqMih1mWa8mlvrRzH_XhZUHnFhUycbHs0xa5ZM,691
cohere/types/parse_info.py,sha256=_P7xwvkQXIyPfxD9i7jevDJl8RN_zeImPZ59ulpRYPE,588
cohere/types/rerank_document.py,sha256=vlpCrRWBC2JOnR2QwoZttKlsYL8wtBgOEIwpDA9la6s,119
cohere/types/rerank_request_documents_item.py,sha256=in0L3TatZSJ_R67NP8RK070OTbDKcp3I_HNCDgdiPP4,188
cohere/types/rerank_response.py,sha256=4LHvtD-qftoirHwrvCF2xYUEK4gVozea1jAjnkq8N-Y,811
cohere/types/rerank_response_results_item.py,sha256=ghMX6L6Rk-sNrOEeRfm4UvvFG4IjMt_Zd7x7iyFadyQ,1514
cohere/types/rerank_response_results_item_document.py,sha256=wpCsH8tzsHvBGjOsXd1lPdbHxuNdmDCAR7jcEDtzQqA,747
cohere/types/reranker_data_metrics.py,sha256=S1RcqQKoT9xrxiZnGW1HKipSkLrdu55tU9GCgsAewdc,1414
cohere/types/response_format.py,sha256=CnTtE9ctzVfPbv9u3z-i1HT_DbiHA7mFi94r6Gdnd3I,3213
cohere/types/response_format_v2.py,sha256=51-i08AkXfi6TRwhko1D594WGu4ZYsBvjtJvkD1XB8Q,3198
cohere/types/single_generation.py,sha256=rzaTpthz5AogsutDFgwgzNq353USl3JT0qdT-eAfT4g,1436
cohere/types/single_generation_in_stream.py,sha256=YVizMElUmjQ4ukPMk_MYDc2uygnis4ZjAL51A6unbXs,856
cohere/types/single_generation_token_likelihoods_item.py,sha256=DzJSTKKKsH6VUclZzfcR0rEvArqZBEnQ2t9pMdT754c,566
cohere/types/source.py,sha256=3sB_pOeUV5WwMgV3GFjFopAw7sclS8njQRJktdXl8G8,1522
cohere/types/streamed_chat_response.py,sha256=ifojgop-DnqIwuoPcrZCxZbGBTInCSjJDM75LOKoK3Y,6321
cohere/types/summarize_request_extractiveness.py,sha256=qQpS2_ziqQPBYJZiJMBkrovDrH9lBNBCKZpmm_iM6vg,179
cohere/types/summarize_request_format.py,sha256=aGXBz0dsQXWQmYtlz2EJBhoTlpLbCF-npB-SHbVuXPM,170
cohere/types/summarize_request_length.py,sha256=hIgrlh-QFmYLPUgYsvsSFw278CKevYhwX9wo1SOc760,173
cohere/types/summarize_response.py,sha256=IPfB9Ea0JvQF0d58W4Vbi8v4wNylR1AtLA1qJPSFoaw,809
cohere/types/system_message_v2.py,sha256=flR6ubyPZqzzymeQpXacFHwre6YCxWh5sVC6ygIufLg,653
cohere/types/system_message_v2content.py,sha256=I6wWHujw6NpKDJ3nqdPa_lWqoKF3LikdARZp9pGQQlU,235
cohere/types/system_message_v2content_item.py,sha256=AGTJ4Nfssu86IFbRMpmPdCvdTA82utGJAlatBT_gYUY,788
cohere/types/thinking.py,sha256=JQi2R_KhkL2FmLNMNe44JLfP3Iw8Uj-7PO_STsNCv4A,1542
cohere/types/thinking_type.py,sha256=PhDyGqDT0KmACJlrClAoFRxI3_gh5WbmHhuF8P0UFI0,159
cohere/types/tokenize_response.py,sha256=0IoDEjcVum06HLRP1eIK-18n-62Wf_e9yf4Q415wHM0,738
cohere/types/tool.py,sha256=4zoVLU9zOKs3Jz5w8s8GHPPHq81brdiZb4Fz-ITABeY,1528
cohere/types/tool_call.py,sha256=AlDUKSzbv-lLPwfcA6Mgg3GLqV2Eufu8ObJyqQnMfhM,846
cohere/types/tool_call_delta.py,sha256=t1fvaPlwvtw9-MN33BGBgxDf_WQJQ_8T4uWwZPcLHkg,1041
cohere/types/tool_call_v2.py,sha256=84vHSscJohY5dO4Tt2LPLrHfd39mdlfy14hAY8gUXFY,766
cohere/types/tool_call_v2function.py,sha256=HVdAxtz4Y2R1kMjNrzVxevC_iwu3G3D9qM9xNFgy-Ak,592
cohere/types/tool_content.py,sha256=mRWf5N7I7UCFOJOFDfurjmx6ZBocOb8NdgpBuKhZMKU,1384
cohere/types/tool_message_v2.py,sha256=OZgjR2iSjlx8xjP1iYj7jrR0QFEijtzah0sH0mZ8LDM,927
cohere/types/tool_message_v2content.py,sha256=4FNxLjyjOGWyTyQ6fxruaZrg0gQT6VRP72Mm2tcd7pw,186
cohere/types/tool_parameter_definitions_value.py,sha256=Hvi0ci4gIyigmDrHiG2GGTSWuASXxG1rZ0ud9ezc1aY,936
cohere/types/tool_result.py,sha256=XfQOxP78UN0iXknfutVhzCbord2Ctr3sfs1Cpb_R8ng,626
cohere/types/tool_v2.py,sha256=SCF61a7s3OJta7u9EhAXYERJgtBc0TbziDGcPVoGAfY,730
cohere/types/tool_v2function.py,sha256=AjgyZVQvvBjY5yzgmZeEWjfCkDLpyfveVZ3Ut_wRZic,910
cohere/types/update_connector_response.py,sha256=8HYLRLlFjZStFh-4SYyNm7QF1Ck-Tdy3qzcM04A_mmE,574
cohere/types/usage.py,sha256=QXjC0JY-A4y1Y8f6ajR-cLCvHvVE72iDmEB1gu7mUAc,692
cohere/types/usage_billed_units.py,sha256=yqvGL8KkzC0o1hFBcyB-CXebiGUi7nS1p3kLwJIjSyk,1034
cohere/types/usage_tokens.py,sha256=619-zAHlkjzZYtC9qfAC9dbL_mSV4C0lSmQFA80L5sg,783
cohere/types/user_message_v2.py,sha256=AsdEHdlexEDajJLI_0WsYBRpR_iZPPRwOym70BIt4GQ,833
cohere/types/user_message_v2content.py,sha256=Ed6kWkWopBKBbYvPtjMxLLpsRv4kOShjd34_6Y-9mK4,173
cohere/utils.py,sha256=lMjbepKOeIYW1VfaaMD-dwgjFAm32DdcXXlDtouVaDo,10186
cohere/v2/__init__.py,sha256=PlmF-V8WzXN2PfLnmSD1_2NjzA7zuWuJrcdXgGXJlcs,1717
cohere/v2/__pycache__/__init__.cpython-39.pyc,,
cohere/v2/__pycache__/client.cpython-39.pyc,,
cohere/v2/__pycache__/raw_client.cpython-39.pyc,,
cohere/v2/client.py,sha256=YYpeFwJpo8mCqRLOVW3t9xenPIV5sIJWkZVDLVg144o,56080
cohere/v2/raw_client.py,sha256=wnSQsa9y8ivSlvAJNbsUOT62yUSveC5X1VpsXqTHtJ0,112418
cohere/v2/types/__init__.py,sha256=lpwdmQNPL---siWld9FKJJ2ucgOjXo1vo8DGpZi7izU,2093
cohere/v2/types/__pycache__/__init__.cpython-39.pyc,,
cohere/v2/types/__pycache__/v2chat_request_documents_item.cpython-39.pyc,,
cohere/v2/types/__pycache__/v2chat_request_safety_mode.cpython-39.pyc,,
cohere/v2/types/__pycache__/v2chat_request_tool_choice.cpython-39.pyc,,
cohere/v2/types/__pycache__/v2chat_response.cpython-39.pyc,,
cohere/v2/types/__pycache__/v2chat_stream_request_documents_item.cpython-39.pyc,,
cohere/v2/types/__pycache__/v2chat_stream_request_safety_mode.cpython-39.pyc,,
cohere/v2/types/__pycache__/v2chat_stream_request_tool_choice.cpython-39.pyc,,
cohere/v2/types/__pycache__/v2chat_stream_response.cpython-39.pyc,,
cohere/v2/types/__pycache__/v2embed_request_truncate.cpython-39.pyc,,
cohere/v2/types/__pycache__/v2rerank_response.cpython-39.pyc,,
cohere/v2/types/__pycache__/v2rerank_response_results_item.cpython-39.pyc,,
cohere/v2/types/v2chat_request_documents_item.py,sha256=WA5uSTxYksNGWb2KS1E9Xzd5EfUo4eZqh4ulDmKcyXY,177
cohere/v2/types/v2chat_request_safety_mode.py,sha256=dY9Wt4v0Jgzlxz7ptUVkvRcbmpsIi3uY54LW1hg2BH0,178
cohere/v2/types/v2chat_request_tool_choice.py,sha256=saYBSxo0_w_fuXy4GtXdzaJasq0EG6SQp-qP7ZPVCSw,167
cohere/v2/types/v2chat_response.py,sha256=kzIE6Q3KznOQTdI5c3TOXjHo-kllEALyqeLfpxDKKRk,1023
cohere/v2/types/v2chat_stream_request_documents_item.py,sha256=kQOP5asDpI8fgCpaIUBcVe2xRLQMe5YpYKULCku9WAo,183
cohere/v2/types/v2chat_stream_request_safety_mode.py,sha256=irPZ_EEt5Ci-vnHdx8-ddQOo2z96eGL7T4ucS1Fhlk8,184
cohere/v2/types/v2chat_stream_request_tool_choice.py,sha256=RimUM2p46FrDC5qHfG2QE6Il_cc-JDRxIUz_fEVGDbM,173
cohere/v2/types/v2chat_stream_response.py,sha256=jq6qLGD2z1nlxQ53EwUsJcIyT-_-zAY45WAJQDMQnhk,8477
cohere/v2/types/v2embed_request_truncate.py,sha256=lA2nzWdWojH73eYdEDoiu1CAqiK3uba0LHhaYd02hVw,170
cohere/v2/types/v2rerank_response.py,sha256=gPcU3KmmkqObT1iuwYpTM773KWQSYSwJ2gyRPZvMRBw,829
cohere/v2/types/v2rerank_response_results_item.py,sha256=QeU6hBnB7fSxf2Ax8gqeMBvWNfAP8u3qcfJytIJQZYE,1204
cohere/version.py,sha256=09AHRA5BDnc2MiTWLtm5ehYUG7qLPP96rmLdpcSZE4o,73
