# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ...core.pydantic_utilities import IS_PYDANTIC_V2
from ...core.unchecked_base_model import UncheckedBaseModel
from ...types.assistant_message_response import AssistantMessageResponse
from ...types.chat_finish_reason import ChatFinishReason
from ...types.logprob_item import LogprobItem
from ...types.usage import Usage


class V2ChatResponse(UncheckedBaseModel):
    id: str = pydantic.Field()
    """
    Unique identifier for the generated reply. Useful for submitting feedback.
    """

    finish_reason: ChatFinishReason
    message: AssistantMessageResponse
    usage: typing.Optional[Usage] = None
    logprobs: typing.Optional[typing.List[LogprobItem]] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
