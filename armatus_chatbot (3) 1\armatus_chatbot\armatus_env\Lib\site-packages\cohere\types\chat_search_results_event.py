# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from .chat_document import ChatDocument
from .chat_search_result import ChatSearchResult
from .chat_stream_event import ChatStreamEvent


class ChatSearchResultsEvent(ChatStreamEvent):
    search_results: typing.Optional[typing.List[ChatSearchResult]] = pydantic.Field(default=None)
    """
    Conducted searches and the ids of documents retrieved from each of them.
    """

    documents: typing.Optional[typing.List[ChatDocument]] = pydantic.Field(default=None)
    """
    Documents fetched from searches or provided by the user.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
