# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations

import typing

import pydantic
import typing_extensions
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel, UnionMetadata


class TextAssistantMessageResponseContentItem(UncheckedBaseModel):
    type: typing.Literal["text"] = "text"
    text: str

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow


class ThinkingAssistantMessageResponseContentItem(UncheckedBaseModel):
    value: typing.Optional[typing.Any] = None
    type: typing.Literal["thinking"] = "thinking"

    if not IS_PYDANTIC_V2:

        class Config:
            smart_union = True


AssistantMessageResponseContentItem = typing_extensions.Annotated[
    typing.Union[TextAssistantMessageResponseContentItem, ThinkingAssistantMessageResponseContentItem],
    UnionMetadata(discriminant="type"),
]
