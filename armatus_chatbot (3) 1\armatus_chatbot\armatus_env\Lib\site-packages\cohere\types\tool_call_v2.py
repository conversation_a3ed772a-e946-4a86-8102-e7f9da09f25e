# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .tool_call_v2function import ToolCallV2Function


class ToolCallV2(UncheckedBaseModel):
    """
    An array of tool calls to be made.
    """

    id: typing.Optional[str] = None
    type: typing.Optional[typing.Literal["function"]] = None
    function: typing.Optional[ToolCallV2Function] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
