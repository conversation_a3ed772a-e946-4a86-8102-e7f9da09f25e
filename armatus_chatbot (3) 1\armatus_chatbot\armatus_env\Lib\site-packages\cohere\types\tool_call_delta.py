# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel


class ToolCallDelta(UncheckedBaseModel):
    """
    Contains the chunk of the tool call generation in the stream.
    """

    name: typing.Optional[str] = pydantic.Field(default=None)
    """
    Name of the tool call
    """

    index: typing.Optional[float] = pydantic.Field(default=None)
    """
    Index of the tool call generated
    """

    parameters: typing.Optional[str] = pydantic.Field(default=None)
    """
    Chunk of the tool parameters
    """

    text: typing.Optional[str] = pydantic.Field(default=None)
    """
    Chunk of the tool plan text
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
