# This file was auto-generated by <PERSON>rn from our API Definition.

import typing

from ..core.client_wrapper import Async<PERSON><PERSON><PERSON>rapper, SyncClientWrapper
from ..core.request_options import RequestOptions
from ..types.create_connector_o_auth import CreateConnectorOAuth
from ..types.create_connector_response import CreateConnectorResponse
from ..types.create_connector_service_auth import CreateConnectorServiceAuth
from ..types.delete_connector_response import DeleteConnectorResponse
from ..types.get_connector_response import GetConnectorResponse
from ..types.list_connectors_response import ListConnectorsResponse
from ..types.o_auth_authorize_response import OAuthAuthorizeResponse
from ..types.update_connector_response import UpdateConnectorResponse
from .raw_client import AsyncRawConnectorsClient, RawConnectorsClient

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class ConnectorsClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawConnectorsClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawConnectorsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawConnectorsClient
        """
        return self._raw_client

    def list(
        self,
        *,
        limit: typing.Optional[float] = None,
        offset: typing.Optional[float] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> ListConnectorsResponse:
        """
        Returns a list of connectors ordered by descending creation date (newer first). See ['Managing your Connector'](https://docs.cohere.com/docs/managing-your-connector) for more information.

        Parameters
        ----------
        limit : typing.Optional[float]
            Maximum number of connectors to return [0, 100].

        offset : typing.Optional[float]
            Number of connectors to skip before returning results [0, inf].

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ListConnectorsResponse
            OK

        Examples
        --------
        from cohere import Client

        client = Client(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )
        client.connectors.list()
        """
        _response = self._raw_client.list(limit=limit, offset=offset, request_options=request_options)
        return _response.data

    def create(
        self,
        *,
        name: str,
        url: str,
        description: typing.Optional[str] = OMIT,
        excludes: typing.Optional[typing.Sequence[str]] = OMIT,
        oauth: typing.Optional[CreateConnectorOAuth] = OMIT,
        active: typing.Optional[bool] = OMIT,
        continue_on_failure: typing.Optional[bool] = OMIT,
        service_auth: typing.Optional[CreateConnectorServiceAuth] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> CreateConnectorResponse:
        """
        Creates a new connector. The connector is tested during registration and will cancel registration when the test is unsuccessful. See ['Creating and Deploying a Connector'](https://docs.cohere.com/v1/docs/creating-and-deploying-a-connector) for more information.

        Parameters
        ----------
        name : str
            A human-readable name for the connector.

        url : str
            The URL of the connector that will be used to search for documents.

        description : typing.Optional[str]
            A description of the connector.

        excludes : typing.Optional[typing.Sequence[str]]
            A list of fields to exclude from the prompt (fields remain in the document).

        oauth : typing.Optional[CreateConnectorOAuth]
            The OAuth 2.0 configuration for the connector. Cannot be specified if service_auth is specified.

        active : typing.Optional[bool]
            Whether the connector is active or not.

        continue_on_failure : typing.Optional[bool]
            Whether a chat request should continue or not if the request to this connector fails.

        service_auth : typing.Optional[CreateConnectorServiceAuth]
            The service to service authentication configuration for the connector. Cannot be specified if oauth is specified.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        CreateConnectorResponse
            OK

        Examples
        --------
        from cohere import Client

        client = Client(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )
        client.connectors.create(
            name="name",
            url="url",
        )
        """
        _response = self._raw_client.create(
            name=name,
            url=url,
            description=description,
            excludes=excludes,
            oauth=oauth,
            active=active,
            continue_on_failure=continue_on_failure,
            service_auth=service_auth,
            request_options=request_options,
        )
        return _response.data

    def get(self, id: str, *, request_options: typing.Optional[RequestOptions] = None) -> GetConnectorResponse:
        """
        Retrieve a connector by ID. See ['Connectors'](https://docs.cohere.com/docs/connectors) for more information.

        Parameters
        ----------
        id : str
            The ID of the connector to retrieve.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetConnectorResponse
            OK

        Examples
        --------
        from cohere import Client

        client = Client(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )
        client.connectors.get(
            id="id",
        )
        """
        _response = self._raw_client.get(id, request_options=request_options)
        return _response.data

    def delete(self, id: str, *, request_options: typing.Optional[RequestOptions] = None) -> DeleteConnectorResponse:
        """
        Delete a connector by ID. See ['Connectors'](https://docs.cohere.com/docs/connectors) for more information.

        Parameters
        ----------
        id : str
            The ID of the connector to delete.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DeleteConnectorResponse
            OK

        Examples
        --------
        from cohere import Client

        client = Client(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )
        client.connectors.delete(
            id="id",
        )
        """
        _response = self._raw_client.delete(id, request_options=request_options)
        return _response.data

    def update(
        self,
        id: str,
        *,
        name: typing.Optional[str] = OMIT,
        url: typing.Optional[str] = OMIT,
        excludes: typing.Optional[typing.Sequence[str]] = OMIT,
        oauth: typing.Optional[CreateConnectorOAuth] = OMIT,
        active: typing.Optional[bool] = OMIT,
        continue_on_failure: typing.Optional[bool] = OMIT,
        service_auth: typing.Optional[CreateConnectorServiceAuth] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> UpdateConnectorResponse:
        """
        Update a connector by ID. Omitted fields will not be updated. See ['Managing your Connector'](https://docs.cohere.com/docs/managing-your-connector) for more information.

        Parameters
        ----------
        id : str
            The ID of the connector to update.

        name : typing.Optional[str]
            A human-readable name for the connector.

        url : typing.Optional[str]
            The URL of the connector that will be used to search for documents.

        excludes : typing.Optional[typing.Sequence[str]]
            A list of fields to exclude from the prompt (fields remain in the document).

        oauth : typing.Optional[CreateConnectorOAuth]
            The OAuth 2.0 configuration for the connector. Cannot be specified if service_auth is specified.

        active : typing.Optional[bool]

        continue_on_failure : typing.Optional[bool]

        service_auth : typing.Optional[CreateConnectorServiceAuth]
            The service to service authentication configuration for the connector. Cannot be specified if oauth is specified.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        UpdateConnectorResponse
            OK

        Examples
        --------
        from cohere import Client

        client = Client(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )
        client.connectors.update(
            id="id",
        )
        """
        _response = self._raw_client.update(
            id,
            name=name,
            url=url,
            excludes=excludes,
            oauth=oauth,
            active=active,
            continue_on_failure=continue_on_failure,
            service_auth=service_auth,
            request_options=request_options,
        )
        return _response.data

    def o_auth_authorize(
        self,
        id: str,
        *,
        after_token_redirect: typing.Optional[str] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> OAuthAuthorizeResponse:
        """
        Authorize the connector with the given ID for the connector oauth app.  See ['Connector Authentication'](https://docs.cohere.com/docs/connector-authentication) for more information.

        Parameters
        ----------
        id : str
            The ID of the connector to authorize.

        after_token_redirect : typing.Optional[str]
            The URL to redirect to after the connector has been authorized.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        OAuthAuthorizeResponse
            OK

        Examples
        --------
        from cohere import Client

        client = Client(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )
        client.connectors.o_auth_authorize(
            id="id",
        )
        """
        _response = self._raw_client.o_auth_authorize(
            id, after_token_redirect=after_token_redirect, request_options=request_options
        )
        return _response.data


class AsyncConnectorsClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawConnectorsClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawConnectorsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawConnectorsClient
        """
        return self._raw_client

    async def list(
        self,
        *,
        limit: typing.Optional[float] = None,
        offset: typing.Optional[float] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> ListConnectorsResponse:
        """
        Returns a list of connectors ordered by descending creation date (newer first). See ['Managing your Connector'](https://docs.cohere.com/docs/managing-your-connector) for more information.

        Parameters
        ----------
        limit : typing.Optional[float]
            Maximum number of connectors to return [0, 100].

        offset : typing.Optional[float]
            Number of connectors to skip before returning results [0, inf].

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ListConnectorsResponse
            OK

        Examples
        --------
        import asyncio

        from cohere import AsyncClient

        client = AsyncClient(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.connectors.list()


        asyncio.run(main())
        """
        _response = await self._raw_client.list(limit=limit, offset=offset, request_options=request_options)
        return _response.data

    async def create(
        self,
        *,
        name: str,
        url: str,
        description: typing.Optional[str] = OMIT,
        excludes: typing.Optional[typing.Sequence[str]] = OMIT,
        oauth: typing.Optional[CreateConnectorOAuth] = OMIT,
        active: typing.Optional[bool] = OMIT,
        continue_on_failure: typing.Optional[bool] = OMIT,
        service_auth: typing.Optional[CreateConnectorServiceAuth] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> CreateConnectorResponse:
        """
        Creates a new connector. The connector is tested during registration and will cancel registration when the test is unsuccessful. See ['Creating and Deploying a Connector'](https://docs.cohere.com/v1/docs/creating-and-deploying-a-connector) for more information.

        Parameters
        ----------
        name : str
            A human-readable name for the connector.

        url : str
            The URL of the connector that will be used to search for documents.

        description : typing.Optional[str]
            A description of the connector.

        excludes : typing.Optional[typing.Sequence[str]]
            A list of fields to exclude from the prompt (fields remain in the document).

        oauth : typing.Optional[CreateConnectorOAuth]
            The OAuth 2.0 configuration for the connector. Cannot be specified if service_auth is specified.

        active : typing.Optional[bool]
            Whether the connector is active or not.

        continue_on_failure : typing.Optional[bool]
            Whether a chat request should continue or not if the request to this connector fails.

        service_auth : typing.Optional[CreateConnectorServiceAuth]
            The service to service authentication configuration for the connector. Cannot be specified if oauth is specified.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        CreateConnectorResponse
            OK

        Examples
        --------
        import asyncio

        from cohere import AsyncClient

        client = AsyncClient(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.connectors.create(
                name="name",
                url="url",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.create(
            name=name,
            url=url,
            description=description,
            excludes=excludes,
            oauth=oauth,
            active=active,
            continue_on_failure=continue_on_failure,
            service_auth=service_auth,
            request_options=request_options,
        )
        return _response.data

    async def get(self, id: str, *, request_options: typing.Optional[RequestOptions] = None) -> GetConnectorResponse:
        """
        Retrieve a connector by ID. See ['Connectors'](https://docs.cohere.com/docs/connectors) for more information.

        Parameters
        ----------
        id : str
            The ID of the connector to retrieve.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetConnectorResponse
            OK

        Examples
        --------
        import asyncio

        from cohere import AsyncClient

        client = AsyncClient(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.connectors.get(
                id="id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.get(id, request_options=request_options)
        return _response.data

    async def delete(
        self, id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> DeleteConnectorResponse:
        """
        Delete a connector by ID. See ['Connectors'](https://docs.cohere.com/docs/connectors) for more information.

        Parameters
        ----------
        id : str
            The ID of the connector to delete.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DeleteConnectorResponse
            OK

        Examples
        --------
        import asyncio

        from cohere import AsyncClient

        client = AsyncClient(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.connectors.delete(
                id="id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.delete(id, request_options=request_options)
        return _response.data

    async def update(
        self,
        id: str,
        *,
        name: typing.Optional[str] = OMIT,
        url: typing.Optional[str] = OMIT,
        excludes: typing.Optional[typing.Sequence[str]] = OMIT,
        oauth: typing.Optional[CreateConnectorOAuth] = OMIT,
        active: typing.Optional[bool] = OMIT,
        continue_on_failure: typing.Optional[bool] = OMIT,
        service_auth: typing.Optional[CreateConnectorServiceAuth] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> UpdateConnectorResponse:
        """
        Update a connector by ID. Omitted fields will not be updated. See ['Managing your Connector'](https://docs.cohere.com/docs/managing-your-connector) for more information.

        Parameters
        ----------
        id : str
            The ID of the connector to update.

        name : typing.Optional[str]
            A human-readable name for the connector.

        url : typing.Optional[str]
            The URL of the connector that will be used to search for documents.

        excludes : typing.Optional[typing.Sequence[str]]
            A list of fields to exclude from the prompt (fields remain in the document).

        oauth : typing.Optional[CreateConnectorOAuth]
            The OAuth 2.0 configuration for the connector. Cannot be specified if service_auth is specified.

        active : typing.Optional[bool]

        continue_on_failure : typing.Optional[bool]

        service_auth : typing.Optional[CreateConnectorServiceAuth]
            The service to service authentication configuration for the connector. Cannot be specified if oauth is specified.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        UpdateConnectorResponse
            OK

        Examples
        --------
        import asyncio

        from cohere import AsyncClient

        client = AsyncClient(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.connectors.update(
                id="id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.update(
            id,
            name=name,
            url=url,
            excludes=excludes,
            oauth=oauth,
            active=active,
            continue_on_failure=continue_on_failure,
            service_auth=service_auth,
            request_options=request_options,
        )
        return _response.data

    async def o_auth_authorize(
        self,
        id: str,
        *,
        after_token_redirect: typing.Optional[str] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> OAuthAuthorizeResponse:
        """
        Authorize the connector with the given ID for the connector oauth app.  See ['Connector Authentication'](https://docs.cohere.com/docs/connector-authentication) for more information.

        Parameters
        ----------
        id : str
            The ID of the connector to authorize.

        after_token_redirect : typing.Optional[str]
            The URL to redirect to after the connector has been authorized.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        OAuthAuthorizeResponse
            OK

        Examples
        --------
        import asyncio

        from cohere import AsyncClient

        client = AsyncClient(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.connectors.o_auth_authorize(
                id="id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.o_auth_authorize(
            id, after_token_redirect=after_token_redirect, request_options=request_options
        )
        return _response.data
