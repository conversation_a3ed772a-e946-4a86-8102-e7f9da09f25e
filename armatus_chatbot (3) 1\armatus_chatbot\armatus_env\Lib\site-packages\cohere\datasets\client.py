# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import datetime as dt
import typing

from .. import core
from ..core.client_wrapper import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SyncClientWrapper
from ..core.request_options import RequestOptions
from ..types.dataset_type import DatasetType
from ..types.dataset_validation_status import DatasetValidationStatus
from .raw_client import AsyncRawDatasetsClient, RawDatasetsClient
from .types.datasets_create_response import DatasetsCreateResponse
from .types.datasets_get_response import DatasetsGetResponse
from .types.datasets_get_usage_response import DatasetsGetUsageResponse
from .types.datasets_list_response import DatasetsListResponse

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class DatasetsClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawDatasetsClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawDatasetsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawDatasetsClient
        """
        return self._raw_client

    def list(
        self,
        *,
        dataset_type: typing.Optional[str] = None,
        before: typing.Optional[dt.datetime] = None,
        after: typing.Optional[dt.datetime] = None,
        limit: typing.Optional[float] = None,
        offset: typing.Optional[float] = None,
        validation_status: typing.Optional[DatasetValidationStatus] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> DatasetsListResponse:
        """
        List datasets that have been created.

        Parameters
        ----------
        dataset_type : typing.Optional[str]
            optional filter by dataset type

        before : typing.Optional[dt.datetime]
            optional filter before a date

        after : typing.Optional[dt.datetime]
            optional filter after a date

        limit : typing.Optional[float]
            optional limit to number of results

        offset : typing.Optional[float]
            optional offset to start of results

        validation_status : typing.Optional[DatasetValidationStatus]
            optional filter by validation status

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DatasetsListResponse
            A successful response.

        Examples
        --------
        from cohere import Client

        client = Client(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )
        client.datasets.list()
        """
        _response = self._raw_client.list(
            dataset_type=dataset_type,
            before=before,
            after=after,
            limit=limit,
            offset=offset,
            validation_status=validation_status,
            request_options=request_options,
        )
        return _response.data

    def create(
        self,
        *,
        name: str,
        type: DatasetType,
        data: core.File,
        keep_original_file: typing.Optional[bool] = None,
        skip_malformed_input: typing.Optional[bool] = None,
        keep_fields: typing.Optional[typing.Union[str, typing.Sequence[str]]] = None,
        optional_fields: typing.Optional[typing.Union[str, typing.Sequence[str]]] = None,
        text_separator: typing.Optional[str] = None,
        csv_delimiter: typing.Optional[str] = None,
        eval_data: typing.Optional[core.File] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> DatasetsCreateResponse:
        """
        Create a dataset by uploading a file. See ['Dataset Creation'](https://docs.cohere.com/docs/datasets#dataset-creation) for more information.

        Parameters
        ----------
        name : str
            The name of the uploaded dataset.

        type : DatasetType
            The dataset type, which is used to validate the data. Valid types are `embed-input`, `reranker-finetune-input`, `single-label-classification-finetune-input`, `chat-finetune-input`, and `multi-label-classification-finetune-input`.

        data : core.File
            See core.File for more documentation

        keep_original_file : typing.Optional[bool]
            Indicates if the original file should be stored.

        skip_malformed_input : typing.Optional[bool]
            Indicates whether rows with malformed input should be dropped (instead of failing the validation check). Dropped rows will be returned in the warnings field.

        keep_fields : typing.Optional[typing.Union[str, typing.Sequence[str]]]
            List of names of fields that will be persisted in the Dataset. By default the Dataset will retain only the required fields indicated in the [schema for the corresponding Dataset type](https://docs.cohere.com/docs/datasets#dataset-types). For example, datasets of type `embed-input` will drop all fields other than the required `text` field. If any of the fields in `keep_fields` are missing from the uploaded file, Dataset validation will fail.

        optional_fields : typing.Optional[typing.Union[str, typing.Sequence[str]]]
            List of names of fields that will be persisted in the Dataset. By default the Dataset will retain only the required fields indicated in the [schema for the corresponding Dataset type](https://docs.cohere.com/docs/datasets#dataset-types). For example, Datasets of type `embed-input` will drop all fields other than the required `text` field. If any of the fields in `optional_fields` are missing from the uploaded file, Dataset validation will pass.

        text_separator : typing.Optional[str]
            Raw .txt uploads will be split into entries using the text_separator value.

        csv_delimiter : typing.Optional[str]
            The delimiter used for .csv uploads.

        eval_data : typing.Optional[core.File]
            See core.File for more documentation

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DatasetsCreateResponse
            A successful response.

        Examples
        --------
        from cohere import Client

        client = Client(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )
        client.datasets.create(
            name="name",
            type="embed-input",
        )
        """
        _response = self._raw_client.create(
            name=name,
            type=type,
            data=data,
            keep_original_file=keep_original_file,
            skip_malformed_input=skip_malformed_input,
            keep_fields=keep_fields,
            optional_fields=optional_fields,
            text_separator=text_separator,
            csv_delimiter=csv_delimiter,
            eval_data=eval_data,
            request_options=request_options,
        )
        return _response.data

    def get_usage(self, *, request_options: typing.Optional[RequestOptions] = None) -> DatasetsGetUsageResponse:
        """
        View the dataset storage usage for your Organization. Each Organization can have up to 10GB of storage across all their users.

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DatasetsGetUsageResponse
            A successful response.

        Examples
        --------
        from cohere import Client

        client = Client(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )
        client.datasets.get_usage()
        """
        _response = self._raw_client.get_usage(request_options=request_options)
        return _response.data

    def get(self, id: str, *, request_options: typing.Optional[RequestOptions] = None) -> DatasetsGetResponse:
        """
        Retrieve a dataset by ID. See ['Datasets'](https://docs.cohere.com/docs/datasets) for more information.

        Parameters
        ----------
        id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DatasetsGetResponse
            A successful response.

        Examples
        --------
        from cohere import Client

        client = Client(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )
        client.datasets.get(
            id="id",
        )
        """
        _response = self._raw_client.get(id, request_options=request_options)
        return _response.data

    def delete(
        self, id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.Dict[str, typing.Optional[typing.Any]]:
        """
        Delete a dataset by ID. Datasets are automatically deleted after 30 days, but they can also be deleted manually.

        Parameters
        ----------
        id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Dict[str, typing.Optional[typing.Any]]
            A successful response.

        Examples
        --------
        from cohere import Client

        client = Client(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )
        client.datasets.delete(
            id="id",
        )
        """
        _response = self._raw_client.delete(id, request_options=request_options)
        return _response.data


class AsyncDatasetsClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawDatasetsClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawDatasetsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawDatasetsClient
        """
        return self._raw_client

    async def list(
        self,
        *,
        dataset_type: typing.Optional[str] = None,
        before: typing.Optional[dt.datetime] = None,
        after: typing.Optional[dt.datetime] = None,
        limit: typing.Optional[float] = None,
        offset: typing.Optional[float] = None,
        validation_status: typing.Optional[DatasetValidationStatus] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> DatasetsListResponse:
        """
        List datasets that have been created.

        Parameters
        ----------
        dataset_type : typing.Optional[str]
            optional filter by dataset type

        before : typing.Optional[dt.datetime]
            optional filter before a date

        after : typing.Optional[dt.datetime]
            optional filter after a date

        limit : typing.Optional[float]
            optional limit to number of results

        offset : typing.Optional[float]
            optional offset to start of results

        validation_status : typing.Optional[DatasetValidationStatus]
            optional filter by validation status

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DatasetsListResponse
            A successful response.

        Examples
        --------
        import asyncio

        from cohere import AsyncClient

        client = AsyncClient(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.datasets.list()


        asyncio.run(main())
        """
        _response = await self._raw_client.list(
            dataset_type=dataset_type,
            before=before,
            after=after,
            limit=limit,
            offset=offset,
            validation_status=validation_status,
            request_options=request_options,
        )
        return _response.data

    async def create(
        self,
        *,
        name: str,
        type: DatasetType,
        data: core.File,
        keep_original_file: typing.Optional[bool] = None,
        skip_malformed_input: typing.Optional[bool] = None,
        keep_fields: typing.Optional[typing.Union[str, typing.Sequence[str]]] = None,
        optional_fields: typing.Optional[typing.Union[str, typing.Sequence[str]]] = None,
        text_separator: typing.Optional[str] = None,
        csv_delimiter: typing.Optional[str] = None,
        eval_data: typing.Optional[core.File] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> DatasetsCreateResponse:
        """
        Create a dataset by uploading a file. See ['Dataset Creation'](https://docs.cohere.com/docs/datasets#dataset-creation) for more information.

        Parameters
        ----------
        name : str
            The name of the uploaded dataset.

        type : DatasetType
            The dataset type, which is used to validate the data. Valid types are `embed-input`, `reranker-finetune-input`, `single-label-classification-finetune-input`, `chat-finetune-input`, and `multi-label-classification-finetune-input`.

        data : core.File
            See core.File for more documentation

        keep_original_file : typing.Optional[bool]
            Indicates if the original file should be stored.

        skip_malformed_input : typing.Optional[bool]
            Indicates whether rows with malformed input should be dropped (instead of failing the validation check). Dropped rows will be returned in the warnings field.

        keep_fields : typing.Optional[typing.Union[str, typing.Sequence[str]]]
            List of names of fields that will be persisted in the Dataset. By default the Dataset will retain only the required fields indicated in the [schema for the corresponding Dataset type](https://docs.cohere.com/docs/datasets#dataset-types). For example, datasets of type `embed-input` will drop all fields other than the required `text` field. If any of the fields in `keep_fields` are missing from the uploaded file, Dataset validation will fail.

        optional_fields : typing.Optional[typing.Union[str, typing.Sequence[str]]]
            List of names of fields that will be persisted in the Dataset. By default the Dataset will retain only the required fields indicated in the [schema for the corresponding Dataset type](https://docs.cohere.com/docs/datasets#dataset-types). For example, Datasets of type `embed-input` will drop all fields other than the required `text` field. If any of the fields in `optional_fields` are missing from the uploaded file, Dataset validation will pass.

        text_separator : typing.Optional[str]
            Raw .txt uploads will be split into entries using the text_separator value.

        csv_delimiter : typing.Optional[str]
            The delimiter used for .csv uploads.

        eval_data : typing.Optional[core.File]
            See core.File for more documentation

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DatasetsCreateResponse
            A successful response.

        Examples
        --------
        import asyncio

        from cohere import AsyncClient

        client = AsyncClient(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.datasets.create(
                name="name",
                type="embed-input",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.create(
            name=name,
            type=type,
            data=data,
            keep_original_file=keep_original_file,
            skip_malformed_input=skip_malformed_input,
            keep_fields=keep_fields,
            optional_fields=optional_fields,
            text_separator=text_separator,
            csv_delimiter=csv_delimiter,
            eval_data=eval_data,
            request_options=request_options,
        )
        return _response.data

    async def get_usage(self, *, request_options: typing.Optional[RequestOptions] = None) -> DatasetsGetUsageResponse:
        """
        View the dataset storage usage for your Organization. Each Organization can have up to 10GB of storage across all their users.

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DatasetsGetUsageResponse
            A successful response.

        Examples
        --------
        import asyncio

        from cohere import AsyncClient

        client = AsyncClient(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.datasets.get_usage()


        asyncio.run(main())
        """
        _response = await self._raw_client.get_usage(request_options=request_options)
        return _response.data

    async def get(self, id: str, *, request_options: typing.Optional[RequestOptions] = None) -> DatasetsGetResponse:
        """
        Retrieve a dataset by ID. See ['Datasets'](https://docs.cohere.com/docs/datasets) for more information.

        Parameters
        ----------
        id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DatasetsGetResponse
            A successful response.

        Examples
        --------
        import asyncio

        from cohere import AsyncClient

        client = AsyncClient(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.datasets.get(
                id="id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.get(id, request_options=request_options)
        return _response.data

    async def delete(
        self, id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.Dict[str, typing.Optional[typing.Any]]:
        """
        Delete a dataset by ID. Datasets are automatically deleted after 30 days, but they can also be deleted manually.

        Parameters
        ----------
        id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Dict[str, typing.Optional[typing.Any]]
            A successful response.

        Examples
        --------
        import asyncio

        from cohere import AsyncClient

        client = AsyncClient(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.datasets.delete(
                id="id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.delete(id, request_options=request_options)
        return _response.data
