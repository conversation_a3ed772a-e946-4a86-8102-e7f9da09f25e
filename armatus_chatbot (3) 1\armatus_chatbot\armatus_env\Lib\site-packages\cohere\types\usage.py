# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .usage_billed_units import UsageBilledUnits
from .usage_tokens import UsageTokens


class Usage(UncheckedBaseModel):
    billed_units: typing.Optional[UsageBilledUnits] = None
    tokens: typing.Optional[UsageTokens] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
