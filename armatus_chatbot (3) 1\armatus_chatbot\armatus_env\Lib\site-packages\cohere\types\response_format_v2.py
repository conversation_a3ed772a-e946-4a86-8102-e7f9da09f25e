# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations

import typing

import pydantic
import typing_extensions
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel, UnionMetadata


class TextResponseFormatV2(UncheckedBaseModel):
    """
    Configuration for forcing the model output to adhere to the specified format. Supported on [Command R](https://docs.cohere.com/v2/docs/command-r), [Command R+](https://docs.cohere.com/v2/docs/command-r-plus) and newer models.

    The model can be forced into outputting JSON objects by setting `{ "type": "json_object" }`.

    A [JSON Schema](https://json-schema.org/) can optionally be provided, to ensure a specific structure.

    **Note**: When using  `{ "type": "json_object" }` your `message` should always explicitly instruct the model to generate a JSON (eg: _"Generate a JSON ..."_) . Otherwise the model may end up getting stuck generating an infinite stream of characters and eventually run out of context length.

    **Note**: When `json_schema` is not specified, the generated object can have up to 5 layers of nesting.

    **Limitation**: The parameter is not supported when used in combinations with the `documents` or `tools` parameters.
    """

    type: typing.Literal["text"] = "text"

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow


class JsonObjectResponseFormatV2(UncheckedBaseModel):
    """
    Configuration for forcing the model output to adhere to the specified format. Supported on [Command R](https://docs.cohere.com/v2/docs/command-r), [Command R+](https://docs.cohere.com/v2/docs/command-r-plus) and newer models.

    The model can be forced into outputting JSON objects by setting `{ "type": "json_object" }`.

    A [JSON Schema](https://json-schema.org/) can optionally be provided, to ensure a specific structure.

    **Note**: When using  `{ "type": "json_object" }` your `message` should always explicitly instruct the model to generate a JSON (eg: _"Generate a JSON ..."_) . Otherwise the model may end up getting stuck generating an infinite stream of characters and eventually run out of context length.

    **Note**: When `json_schema` is not specified, the generated object can have up to 5 layers of nesting.

    **Limitation**: The parameter is not supported when used in combinations with the `documents` or `tools` parameters.
    """

    type: typing.Literal["json_object"] = "json_object"
    json_schema: typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow


ResponseFormatV2 = typing_extensions.Annotated[
    typing.Union[TextResponseFormatV2, JsonObjectResponseFormatV2], UnionMetadata(discriminant="type")
]
