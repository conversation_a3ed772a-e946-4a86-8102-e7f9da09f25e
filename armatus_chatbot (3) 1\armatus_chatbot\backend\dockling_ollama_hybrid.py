from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import os
import threading
import sys
import traceback
from pathlib import Path
from typing import List, Dict, Any, Optional
import re
import json
from werkzeug.utils import secure_filename
import numpy as np
import uuid
import time
from datetime import datetime, timedelta
import requests
import json
from typing import Optional,Dict,List
from groq import Groq

# For PDF processing
try:
    import PyPDF2
except ImportError:
    print("⚠️ PyPDF2 not installed. Install with: pip install PyPDF2")

# For DOCX processing
try:
    from docx import Document
except ImportError:
    print("⚠️ python-docx not installed. Install with: pip install python-docx")



# For Flask endpoints
from werkzeug.utils import secure_filename
# Import for XLSX processing
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    print("⚠️ pandas not available - XLSX processing will be limited")
    PANDAS_AVAILABLE = False

# Try to import Docling with proper error handling
try:
    from docling.document_converter import DocumentConverter
    from docling.datamodel.base_models import InputFormat
    from docling.datamodel.pipeline_options import PdfPipelineOptions
    DOCLING_AVAILABLE = True
except ImportError as e:
    print(f"❌ Failed to import Docling: {e}")
    print("Please install Docling: pip install docling")
    DOCLING_AVAILABLE = False

# Try to import optional dependencies for hybrid search
try:
    from sklearn.metrics.pairwise import cosine_similarity
    from sklearn.feature_extraction.text import TfidfVectorizer
    SKLEARN_AVAILABLE = True
except ImportError:
    print("⚠️ scikit-learn not available - using basic search")
    SKLEARN_AVAILABLE = False

try:
    from rank_bm25 import BM25Okapi
    BM25_AVAILABLE = True
except ImportError:
    print("⚠️ rank-bm25 not available - using TF-IDF fallback")
    BM25_AVAILABLE = False

try:
    import openai
    OPENAI_AVAILABLE = True
    GROQ_AVAILABLE = True
except ImportError:
    print("⚠️ OpenAI not available - using basic embeddings")
    OPENAI_AVAILABLE = False
    GROQ_AVAILABLE = False
document_chunks=[]
class JurisdictionRulesEngine:
    """Enhanced rules engine that handles state vs manufacturer priority and XLSX statutory rules"""
    
    def __init__(self):
        self.jurisdiction_hierarchy = {
            'federal': 1,  # Highest priority
            'state': 2,    # Second priority  
            'manufacturer': 3,  # Lowest priority
            'local': 4     # Lowest
        }
        self.statutory_rules = {}  # Will store XLSX rules
        
    def load_statutory_rules_from_xlsx(self, xlsx_content: str, filename: str):
        """Parse and load statutory rules from XLSX content"""
        try:
            import re
            
            # Extract rules from the processed XLSX content
            rules = {
                'manufacturer': None,
                'state': None,
                'parts_rules': [],
                'labor_rules': [],
                'exclusions': [],
                'inclusions': [],
                'jurisdiction_notes': []
            }
            
            # Parse manufacturer
            manufacturer_match = re.search(r'Manufacturer:\s*(\w+)', xlsx_content)
            if manufacturer_match:
                rules['manufacturer'] = manufacturer_match.group(1).upper()
            
            # Parse parts and labor rules
            lines = xlsx_content.split('\n')
            current_section = None
            
            for line in lines:
                line = line.strip()
                
                # Identify sections
                if 'Parts' in line and 'Labor' in line and 'Description' in line:
                    current_section = 'rules_section'
                    continue
                
                if current_section == 'rules_section' and line:
                    # Parse Include/Exclude patterns
                    if 'Include' in line or 'Exclude' in line:
                        parts = line.split('|') if '|' in line else line.split('\t')
                        
                        if len(parts) >= 4:
                            parts_rule = parts[0].strip()
                            labor_rule = parts[1].strip() 
                            description = parts[2].strip()
                            notes = parts[3].strip() if len(parts) > 3 else ''
                            
                            rule_entry = {
                                'parts': parts_rule,
                                'labor': labor_rule,
                                'description': description,
                                'notes': notes,
                                'source_file': filename
                            }
                            
                            if 'Include' in parts_rule:
                                rules['inclusions'].append(rule_entry)
                            elif 'Exclude' in parts_rule:
                                rules['exclusions'].append(rule_entry)
            
            self.statutory_rules[filename] = rules
            print(f"✅ Loaded statutory rules for {rules.get('manufacturer', 'Unknown')} from {filename}")
            return rules
            
        except Exception as e:
            print(f"❌ Error loading statutory rules from {filename}: {e}")
            return {}
    
    def determine_jurisdiction_priority(self, query: str, available_documents: List[Dict]) -> List[Dict]:
        """Determine jurisdiction priority based on query and available documents"""
        try:
            query_lower = query.lower()
            
            # Detect state mentions in query
            state_keywords = {
                'california': 'CA', 'florida': 'FL', 'texas': 'TX', 'new york': 'NY',
                'michigan': 'MI', 'ohio': 'OH', 'illinois': 'IL', 'pennsylvania': 'PA'
            }
            
            detected_state = None
            for state_name, state_code in state_keywords.items():
                if state_name in query_lower:
                    detected_state = state_code
                    break
            
            # Prioritize documents by jurisdiction
            prioritized_docs = []
            
            for doc in available_documents:
                doc_name = doc.get('filename', '').lower()
                doc_category = doc.get('document_category', '').lower()
                doc_content = doc.get('content', '').lower()
                
                # Determine document jurisdiction
                jurisdiction_score = 0
                doc_jurisdiction = 'manufacturer'  # Default
                
                # Check if it's a state document
                if any(state in doc_name or state in doc_content for state in state_keywords.keys()):
                    doc_jurisdiction = 'state'
                    jurisdiction_score = self.jurisdiction_hierarchy['state']
                elif 'federal' in doc_name or 'federal' in doc_category:
                    doc_jurisdiction = 'federal'  
                    jurisdiction_score = self.jurisdiction_hierarchy['federal']
                elif any(manufacturer in doc_name for manufacturer in ['ford', 'toyota', 'honda', 'gm','Chrysler']):
                    doc_jurisdiction = 'manufacturer'
                    jurisdiction_score = self.jurisdiction_hierarchy['manufacturer']
                
                # Boost score if state matches query
                if detected_state and detected_state.lower() in doc_content:
                    jurisdiction_score -= 0.5  # Higher priority (lower number)
                
                doc_copy = doc.copy()
                doc_copy['jurisdiction'] = doc_jurisdiction
                doc_copy['jurisdiction_score'] = jurisdiction_score
                doc_copy['state_match'] = detected_state and detected_state.lower() in doc_content
                
                prioritized_docs.append(doc_copy)
            
            # Sort by jurisdiction priority
            prioritized_docs.sort(key=lambda x: x['jurisdiction_score'])
            
            print(f" Jurisdiction priority determined. Detected state: {detected_state}")
            # print(f"Document order: {[f/"{doc['filename']} ({doc['jurisdiction']})/" for doc in prioritized_docs[:3]]}")
            
            return prioritized_docs
            
        except Exception as e:
            print(f"❌ Error in jurisdiction priority: {e}")
            return available_documents

    def apply_statutory_rules(self, query: str, search_results: List[Dict]) -> Dict:
        """Apply statutory rules to determine include/exclude based on XLSX rules"""
        try:
            query_lower = query.lower()
            
            # Find applicable statutory rules
            applicable_rules = []
            manufacturer_detected = None
            state_detected = None
            
            # Detect manufacturer and state from query
            manufacturers = ['ford', 'toyota', 'honda', 'gm', 'bmw', 'mercedes','Chrysler']
            states = ['california', 'florida', 'texas', 'new york', 'michigan']
            
            for mfg in manufacturers:
                if mfg in query_lower:
                    manufacturer_detected = mfg.upper()
                    break
                    
            for state in states:
                if state in query_lower:
                    state_detected = state.title()
                    break
            
            # Get relevant statutory rules
            for filename, rules in self.statutory_rules.items():
                if manufacturer_detected and rules.get('manufacturer') == manufacturer_detected:
                    applicable_rules.append(rules)
            
            if not applicable_rules:
                return {
                    'status': 'no_applicable_rules',
                    'manufacturer': manufacturer_detected,
                    'state': state_detected,
                    'filtered_results': search_results,
                    'applied_rules': []
                }
            
            # Apply inclusion/exclusion rules
            filtered_results = []
            applied_rules = []
            
            for result in search_results:
                content = result.get('content', '').lower()
                description = result.get('description', '').lower()
                
                include_result = True
                rule_reason = "default inclusion"
                
                # Check exclusion rules first (they take priority)
                for rules in applicable_rules:
                    for exclusion in rules.get('exclusions', []):
                        desc = exclusion.get('description', '').lower()
                        notes = exclusion.get('notes', '').lower()
                        
                        # Check if exclusion applies
                        if desc and any(keyword in content for keyword in desc.split()):
                            # Special handling for state-specific rules
                            if state_detected and 'california' in state_detected.lower():
                                # California state law might override manufacturer exclusion
                                if 'warranty' in query_lower and 'include only with larger warranty' in notes:
                                    include_result = True
                                    rule_reason = f"California state law overrides manufacturer exclusion: {desc}"
                                else:
                                    include_result = False
                                    rule_reason = f"Excluded by rule: {desc}"
                            else:
                                include_result = False
                                rule_reason = f"Excluded by rule: {desc}"
                            
                            applied_rules.append({
                                'type': 'exclusion',
                                'description': desc,
                                'notes': notes,
                                'applied_to': result.get('section', 'unknown section')
                            })
                            break
                
                # Check inclusion rules if not already excluded
                if include_result:
                    for rules in applicable_rules:
                        for inclusion in rules.get('inclusions', []):
                            desc = inclusion.get('description', '').lower()
                            notes = inclusion.get('notes', '').lower()
                            
                            if desc and any(keyword in content for keyword in desc.split()):
                                include_result = True
                                rule_reason = f"Included by rule: {desc}"
                                
                                applied_rules.append({
                                    'type': 'inclusion',
                                    'description': desc,
                                    'notes': notes,
                                    'applied_to': result.get('section', 'unknown section')
                                })
                                break
                
                if include_result:
                    result_copy = result.copy()
                    result_copy['rule_reason'] = rule_reason
                    result_copy['statutory_compliance'] = True
                    filtered_results.append(result_copy)
            
            print(f" Applied statutory rules: {len(applied_rules)} rules processed")
            print(f" Results filtered from {len(search_results)} to {len(filtered_results)}")
            
            return {
                'status': 'rules_applied',
                'manufacturer': manufacturer_detected,
                'state': state_detected,
                'filtered_results': filtered_results,
                'applied_rules': applied_rules,
                'jurisdiction_priority': f"State: {state_detected}, Manufacturer: {manufacturer_detected}"
            }
            
        except Exception as e:
            print(f"❌ Error applying statutory rules: {e}")
            return {
                'status': 'error',
                'message': str(e),
                'filtered_results': search_results
            }
# Metadata Generation LLM Client
class ExtractionLLM:
    """Specialized LLM for extracting structured data (state, manufacturer, etc.)"""
    
    def __init__(self):
        self.groq_client = None
        self.openai_client = None
        
    def initialize_groq(self, api_key: str):
        if GROQ_AVAILABLE and api_key:
            try:
                self.groq_client = Groq(api_key=api_key)
                return True
            except Exception as e:
                print(f"Failed to initialize Groq for extraction: {e}")
                return False
        return False
    
    def initialize_openai(self, api_key: str):
        if OPENAI_AVAILABLE and api_key:
            try:
                self.openai_client = openai.OpenAI(api_key=api_key)
                return True
            except Exception as e:
                print(f"Failed to initialize OpenAI for extraction: {e}")
                return False
        return False
    

    def extract_entities(self, text: str, query: str) -> Dict:
        """Extract structured entities from text based on query with concise legal answers"""

        extraction_prompt = """You are an expert legal document assistant specializing in providing direct, accurate answers to legal questions.
        Your task is to analyze the provided document context and give a precise, actionable answer to the user's question.    
        Guidelines for your responses:
        1.Answer: 2 words max 
        2.Concise: Direct, brief response 
        3.Sources: Cite briefly, without legal terms 
        4.Output: Layman terms, 5 words max 
        5.Avoid: Legal jargon and lengthy explanations
        Remember: You are answering based ONLY on the provided legal document context. Be precise and authoritative within that scope."""
        try:
            if self.groq_client:
                response = self._extract_with_groq(extraction_prompt)
            elif self.openai_client:
                response = self._extract_with_openai(extraction_prompt)
            else:
                return self._fallback_extraction(text, query)
            
            if response:
                try:
                    import json
                    return json.loads(response)
                except json.JSONDecodeError:
                    print("Failed to parse extraction response as JSON")
                    return self._fallback_extraction(text, query)
                    
        except Exception as e:
            print(f"Error in entity extraction: {e}")
            return self._fallback_extraction(text, query)
    def delete_document_data(self, document_id: str, file_name: str) -> bool:
        """Delete extraction data for a specific document"""
        try:
            # Delete extraction cache if exists
            extraction_cache_path = f"cache/extraction_{document_id}.json"
            if os.path.exists(extraction_cache_path):
                os.remove(extraction_cache_path)
            
            # Delete any temporary extraction files
            temp_extraction_dir = f"temp/extraction_{document_id}"
            if os.path.exists(temp_extraction_dir):
                shutil.rmtree(temp_extraction_dir)
            
            print(f"Deleted extraction data for document: {document_id}")
            return True
        except Exception as e:
            print(f"Failed to delete extraction data for {document_id}: {e}")
            return False
    def _extract_with_groq(self, prompt: str, text: str, query: str) -> str:
        """Extract entities using Groq with improved error handling and context"""
        try:
            # Enhanced system prompt for better JSON structure
            system_prompt = """You are a precise legal document data extraction specialist. 
            Always respond in valid JSON format with the following structure:
            {
                "answer": "brief answer (max 2 words)",
                "explanation": "concise explanation (max 5 words)", 
                "confidence": "high/medium/low",
                "source_reference": "brief citation if available"
            }
            If information is not found, return: {"answer": "not found", "explanation": "no relevant information", "confidence": "high", "source_reference": "none"}"""
            
            # Combine the extraction prompt with actual text and query
            user_prompt = f"""
            {prompt}
            
            Document Context:
            {text[:3000]}  # Limit context to avoid token limits
            
            User Question: {query}
            
            Please extract the relevant information and respond in the specified JSON format.
            """
            
            response = self.groq_client.chat.completions.create(
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                # model="groq/compound",
                model="llama-3.3-70b-versatile",  # Updated to more stable model
                temperature=0.0,  # Use 0 for consistent extraction
                max_tokens=500,   # Reduced for concise responses
                top_p=0.95,       # Slightly higher for better coherence
                stream=False
            )
            print("extcation response",response)
            # content = response
            content = response.choices[0].message.content.strip()
            print(f"Groq response: {content}")
            # Clean up common JSON formatting issues
            content = self._clean_json_response(content)
            
            # Validate JSON format
            import json
            try:
                parsed_json = json.loads(content)
                # Ensure required fields exist
                required_fields = ["answer", "explanation", "confidence", "source_reference"]
                if all(field in parsed_json for field in required_fields):
                    return content
                else:
                    print(f"Missing required fields in JSON: {parsed_json}")
                    return self._create_fallback_json("incomplete response")
            except json.JSONDecodeError as json_err:
                print(f"Invalid JSON returned: {content}")
                print(f"JSON Error: {json_err}")
                return self._create_fallback_json("json parse error")
                            
        except Exception as e:
            print(f"Groq extraction error: {e}")
            return self._create_fallback_json(f"api error: {str(e)}")

    def _clean_json_response(self, content: str) -> str:
        """Clean common JSON formatting issues from LLM responses"""
        # Remove markdown code blocks if present
        if "```json" in content:
            content = content.split("```json")[1].split("```")[0]
        elif "```" in content:
            content = content.split("```")[1].split("```")[0]
        
        # Remove leading/trailing whitespace and newlines
        content = content.strip()
        
        # Ensure content starts and ends with braces
        if not content.startswith("{"):
            # Try to find the first opening brace
            start_idx = content.find("{")
            if start_idx != -1:
                content = content[start_idx:]
        
        if not content.endswith("}"):
            # Try to find the last closing brace
            end_idx = content.rfind("}")
            if end_idx != -1:
                content = content[:end_idx + 1]
        
        return content

    def _create_fallback_json(self, error_reason: str) -> str:
        """Create a fallback JSON response when extraction fails"""
        import json
        fallback = {
            "answer": "error",
            "explanation": f"extraction failed: {error_reason}",
            "confidence": "low",
            "source_reference": "none"
        }
        return json.dumps(fallback)

    def extract_entities(self, text: str, query: str) -> Dict:
        """Extract structured entities from text based on query with concise legal answers"""
        
        extraction_prompt = """You are an expert legal document assistant specializing in providing direct, accurate answers to legal questions.
        Your task is to analyze the provided document context and give a precise, actionable answer to the user's question.    
        
        Guidelines for your responses:
        1. Answer: 2 words maximum - be direct and specific
        2. Concise: Direct, brief response without unnecessary elaboration
        3. Sources: Cite briefly, without complex legal terminology
        4. Output: Use layman terms, explanation maximum 5 words
        5. Avoid: Legal jargon and lengthy explanations
        6. Format: Always respond in valid JSON format
        
        Remember: You are answering based ONLY on the provided legal document context. Be precise and authoritative within that scope."""
        
        try:
            if self.groq_client:
                response = self._extract_with_groq(extraction_prompt, text, query)
            elif self.openai_client:
                response = self._extract_with_openai(extraction_prompt, text, query)
            else:
                return self._fallback_extraction(text, query)
            
            if response:
                try:
                    import json
                    result = json.loads(response)
                    
                    # Validate and clean the response
                    if isinstance(result, dict):
                        return result
                    else:
                        print(f"Invalid response format: {result}")
                        return self._fallback_extraction(text, query)
                        
                except json.JSONDecodeError as e:
                    print(f"Failed to parse extraction response as JSON: {e}")
                    print(f"Raw response: {response}")
                    return self._fallback_extraction(text, query)
                    
        except Exception as e:
            print(f"Error in entity extraction: {e}")
            return self._fallback_extraction(text, query)
    
    
    
    def _extract_with_openai(self, prompt: str) -> str:
        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are a precise legal document data extraction specialist. Always respond in valid JSON format."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=1000
            )
            return response.choices[0].message.content
        except Exception as e:
            print(f"OpenAI extraction error: {e}")
            return None
    
    def _fallback_extraction(self, text: str, query: str) -> Dict:
        """Rule-based fallback extraction"""
        import re
        
        # Simple pattern-based extraction
        state_match = re.search(r'\b(Florida|California|Texas|New York|Alabama|Alaska)\b', text, re.IGNORECASE)
        section_matches = re.findall(r'\b\d+\.\d+\b', text)
        manufacturer_match = re.search(r'\b(Ford|Toyota|Honda|BMW|GM|Mercedes|Tesla|Chrysler)\b', text, re.IGNORECASE)
        timeline_match = re.search(r'\b(\d+)\s+(days?|months?)\b', text, re.IGNORECASE)
        
        return {
            "state": state_match.group(1) if state_match else None,
            "manufacturer": manufacturer_match.group(1) if manufacturer_match else None,
            "legal_sections": section_matches[:3],
            "timeline": timeline_match.group(0) if timeline_match else None,
            "monetary_amounts": re.findall(r'\$\d+(?:,\d{3})*(?:\.\d{2})?', text),
            "key_terms": ["warranty", "reimbursement", "notice"] if any(term in text.lower() for term in ["warranty", "reimbursement", "notice"]) else [],
            "procedure_type": "warranty" if "warranty" in text.lower() else "general",
            "confidence_score": 0.6,
            "extracted_facts": [text[:200] + "..."]
        }

class RAGBasedLLM:
    """RAG-based LLM for retrieving and generating contextual responses"""
    
    def __init__(self):
        self.groq_client = None
        self.openai_client = None
        
    def initialize_groq(self, api_key: str):
        if GROQ_AVAILABLE and api_key:
            try:
                self.groq_client = Groq(api_key=api_key)
                return True
            except Exception as e:
                print(f"Failed to initialize Groq for RAG: {e}")
                return False
        return False
    
    def initialize_openai(self, api_key: str):
        if OPENAI_AVAILABLE and api_key:
            try:
                self.openai_client = openai.OpenAI(api_key=api_key)
                return True
            except Exception as e:
                print(f"Failed to initialize OpenAI for RAG: {e}")
                return False
        return False
    
    def generate_rag_response(self, query: str, search_results: List[Dict], extracted_entities: Dict) -> Dict:
        """Generate comprehensive response using RAG approach with extracted entities"""
        
        # Prepare context from search results
        context_parts = []
        for i, result in enumerate(search_results[:4], 1):
            context_parts.append(f"""
        Source {i}:
        Section: {result.get('section', 'Unknown')}
        Document: {result.get('document_label', result.get('file_name', ''))}
        Content: {result.get('content', '')[:800]}
        Relevance: {result.get('relevanceScore', 0):.2f}
        """)                
        context = "\n".join(context_parts)                
        rag_prompt = f"""You are an expert legal document assistant specializing in providing direct, accurate answers to legal questions.
        Your task is to analyze the provided document context and give a precise, actionable answer to the user's question.
        Guidelines for your responses:
        1.Answer: 2 words max      
        2.Concise: Direct, brief response        
        3.Sources: Cite briefly, without legal terms        
        4.Output: Layman terms, 5 words max        
        5.Avoid: Legal jargon and lengthy explanations
        Remember: You are answering based ONLY on the provided legal document context. Be precise and authoritative within that scope."
        Extracted Entities:
        - State/Jurisdiction: {extracted_entities.get('state', 'Not specified')}
        - Manufacturer: {extracted_entities.get('manufacturer', 'Not specified')}
        - Legal Sections: {', '.join(extracted_entities.get('legal_sections', []))}
        - Timeline: {extracted_entities.get('timeline', 'Not specified')}
        - Procedure Type: {extracted_entities.get('procedure_type', 'General')}
        - Key Terms: {', '.join(extracted_entities.get('key_terms', []))}
        User Query: {query}
        Retrieved Document Context:
        {context}
        # Your task is to:
        # 1. Synthesize information across all sources
        # 2. Handle complex rule processing and contextual nuances
        # 3. Cross-reference information between sources
        # 4. Identify any conflicts or complementary information
        # 5. Apply multi-source logic to provide comprehensive analysis

        # Provide a detailed response that:
        # - Directly answers the user's question
        # - References specific legal sections and sources
        # - Explains any complex relationships or dependencies
        # - Notes any jurisdictional or manufacturer-specific variations
        # - Highlights important procedural requirements or timelines
        Response format:
        {{
            "main_answer": "Direct answer to the query",
            "supporting_analysis": "Detailed analysis and cross-references",
            "source_synthesis": "How different sources relate to each other",
            "procedural_requirements": "Step-by-step requirements if applicable",
            "important_notes": "Critical considerations or exceptions",
            "confidence_level": 0.85,
            "sources_used": ["list of primary sources referenced"]
        }}
        Respond in JSON format."""

        try:
            if self.groq_client:
                response = self._generate_with_groq(rag_prompt)
                print("EXTARCT RESULT",response)
                return response
            elif self.openai_client:
                response = self._generate_with_openai(rag_prompt)
                return response
            else:
                return self._fallback_rag_response(query, search_results, extracted_entities)
            
            # if response:
            #     try:
            #         # import json
            #         # return json.loads(response)
            #         retrurn response
            #     except json.JSONDecodeError:
            #         print("Failed to parse RAG response as JSON")
            #         return self._fallback_rag_response(query, search_results, extracted_entities)
                    
        except Exception as e:
            print(f"Error in RAG generation: {e}")
            return self._fallback_rag_response(query, search_results, extracted_entities)
    
    def _generate_with_groq(self, prompt: str) -> str:
        try:
            response = self.groq_client.chat.completions.create(
                messages=[
                    {"role": "system", "content": "You are an expert legal document analyst. Always respond in valid JSON format with comprehensive analysis."},
                    {"role": "user", "content": prompt}
                ],
                # model="groq/compound",
                model="llama-3.3-70b-versatile",
                temperature=0.2,
                max_tokens=2000
            )
            return response.choices[0].message.content
        except Exception as e:
            print(f"Groq RAG error: {e}")
            return None
    
    def _generate_with_openai(self, prompt: str) -> str:
        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are an expert legal document analyst. Always respond in valid JSON format with comprehensive analysis."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.2,
                max_tokens=2000
            )
            return response.choices[0].message.content
        except Exception as e:
            print(f"OpenAI RAG error: {e}")
            return None
    
    def _fallback_rag_response(self, query: str, search_results: List[Dict], extracted_entities: Dict) -> Dict:
        """Fallback RAG response generation"""
        if not search_results:
            return {
                "main_answer": f"No relevant information found for '{query}'.",
                "supporting_analysis": "Unable to locate relevant legal documents.",
                "source_synthesis": "No sources available for analysis.",
                "procedural_requirements": "Cannot determine requirements without source documents.",
                "important_notes": "Please ensure legal documents are properly processed.",
                "confidence_level": 0.0,
                "sources_used": []
            }
        
        best_result = search_results[0]
        return {
            "main_answer": f"Based on {best_result.get('section', 'the relevant section')}, {best_result.get('content', '')[:200]}...",
            "supporting_analysis": f"Analysis based on extracted entities: State={extracted_entities.get('state')}, Manufacturer={extracted_entities.get('manufacturer')}",
            "source_synthesis": f"Primary source: {best_result.get('document_label', 'Legal Document')}",
            "procedural_requirements": "Specific procedures would require manual review of the complete document.",
            "important_notes": "This is a simplified analysis. Full legal review recommended.",
            "confidence_level": 0.6,
            "sources_used": [best_result.get('section', 'Unknown Section')]
        }
    def delete_document_from_rag(self, document_id: str, file_name: str) -> bool:
        """Remove document from RAG system"""
        try:
            # Delete RAG cache
            rag_cache_path = f"cache/rag_{document_id}.json"
            if os.path.exists(rag_cache_path):
                os.remove(rag_cache_path)
            
            # Delete document-specific RAG index
            rag_index_path = f"indexes/rag_{document_id}.pkl"
            if os.path.exists(rag_index_path):
                os.remove(rag_index_path)
            
            print(f"Deleted RAG data for document: {document_id}")
            return True
        except Exception as e:
            print(f"Failed to delete RAG data for {document_id}: {e}")
            return False



class FinalAnswerGeneratorLLM:
    """Final answer generator that synthesizes extraction and RAG results"""
    
    def __init__(self):
        self.groq_client = None
        self.openai_client = None
        
    def initialize_groq(self, api_key: str):
        if GROQ_AVAILABLE and api_key:
            try:
                self.groq_client = Groq(api_key=api_key)
                return True
            except Exception as e:
                print(f"Failed to initialize Groq for final answer: {e}")
                return False
        return False
    
    def initialize_openai(self, api_key: str):
        if OPENAI_AVAILABLE and api_key:
            try:
                self.openai_client = openai.OpenAI(api_key=api_key)
                return True
            except Exception as e:
                print(f"Failed to initialize OpenAI for final answer: {e}")
                return False
        return False
    
    def generate_final_answer(self, query: str, extraction_result: Dict, rag_result: Dict, 
                            user_preferences: Dict = None, *args, **kwargs) -> str:
        """Generate final polished answer based on extraction and RAG results"""
        
        user_preferences = user_preferences or {}
        print(f"User Preferences: {user_preferences}")
        answer_style = user_preferences.get('style', 'detailed')
        include_sources = user_preferences.get('include_sources', True)
        confidence_threshold = user_preferences.get('confidence_threshold', 0.7)
        print("extraction result----",extraction_result)
        print("rag result---",rag_result)          
        final_prompt = f"""You are a final answer generator that creates polished, user-friendly responses based on extracted data and RAG analysis.

            User Query: {query}
            Extracted Entities:
            {json.dumps(extraction_result, indent=2)}
            RAG Analysis:
            {json.dumps(rag_result, indent=2)}
            User Preferences:
            - Answer Style: {answer_style}
            - Include Sources: {include_sources}
            - Confidence Threshold: {confidence_threshold}
            1.Answer: 2 words max        
            2.Concise: Direct, brief response        
            3.Sources: Cite briefly, without legal terms        
            4.Output: Layman terms, 5 words max        
            5.Avoid: Legal jargon and lengthy explanations
            Answer Style Guidelines:
            - "detailed": Comprehensive explanation with context"""
        try:
            if self.groq_client:
                response = self._generate_with_groq(final_prompt)
                print(response,"response.....................=====")
            elif self.openai_client:
                response = self._generate_with_openai(final_prompt)
            else:
                return self._fallback_final_answer(query, extraction_result, rag_result, user_preferences)
            
            if response:
                return self._post_process_answer(response, extraction_result, rag_result, user_preferences)
            else:
                return self._fallback_final_answer(query, extraction_result, rag_result, user_preferences)
                
        except Exception as e:
            print(f"Error in final answer generation: {e}")
            return self._fallback_final_answer(query, extraction_result, rag_result, user_preferences)
    
    def _generate_with_groq(self, prompt: str) -> str:
        try:
            response = self.groq_client.chat.completions.create(
                messages=[
                    {"role": "system", "content": "You are a helpful legal document assistant. Provide clear, concise answers in plain English. Avoid legal jargon and keep responses brief but complete."},
                    {"role": "user", "content": prompt}
                ],
                model="llama-3.3-70b-versatile",
                # model="groq/compound",
                temperature=0.1,
                max_tokens=500  # Reduced for more concise answers
            )
            print(response.choices[0].message.content,"response[0].......")
            return response.choices[0].message.content
        except Exception as e:
            print(f"Groq final answer error: {e}")
            return None
    
    def _generate_with_openai(self, prompt: str) -> str:
        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are a helpful legal document assistant. Provide clear, concise answers in plain English. Avoid legal jargon and keep responses brief but complete."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=500  # Reduced for more concise answers
            )
            return response.choices[0].message.content
        except Exception as e:
            print(f"OpenAI final answer error: {e}")
            return None
    
    def _post_process_answer(self, answer: str, extraction_result: Dict, rag_result: Dict, 
                           user_preferences: Dict) -> str:
        """Post-process the generated answer for user-friendly format"""
        
        # Debug all parameters to find which one is a string
        print(f"answer type: {type(answer)}")
        print(f"extraction_result type: {type(extraction_result)}")
        print(f"extraction_result content: {extraction_result}")
        print(f"rag_result type: {type(rag_result)}")
        print(f"rag_result content: {rag_result}")
        print(f"user_preferences type: {type(user_preferences)}")
        print(f"user_preferences content: {user_preferences}")
        
        # Handle case where any parameter might be a string or None
        if isinstance(extraction_result, str):
            print("Warning: extraction_result is a string, converting to empty dict")
            extraction_result = {}
        elif extraction_result is None:
            extraction_result = {}
        elif not isinstance(extraction_result, dict):
            print(f"Warning: extraction_result is {type(extraction_result)}, using empty dict")
            extraction_result = {}
        
        if isinstance(rag_result, str):
            print("Warning: rag_result is a string, converting to empty dict")
            rag_result = {}
        elif rag_result is None:
            rag_result = {}
        elif not isinstance(rag_result, dict):
            print(f"Warning: rag_result is {type(rag_result)}, using empty dict")
            rag_result = {}
        
        if isinstance(user_preferences, str):
            print("Warning: user_preferences is a string, converting to empty dict")
            user_preferences = {}
        elif user_preferences is None:
            print("Warning: user_preferences is None, using empty dict")
            user_preferences = {}
        elif not isinstance(user_preferences, dict):
            print(f"Warning: user_preferences is {type(user_preferences)}, using empty dict")
            user_preferences = {}        
    
        confidence_threshold = user_preferences.get('confidence_threshold', 0.7)
        include_sources = user_preferences.get('include_sources', True)
        
        # Clean up the answer
        answer = answer.strip()
        
        # Add essential context if missing
        if not answer.lower().startswith(('based on', 'according to', 'the ', 'in ')):
            # Try to add document context
            doc_name = self._extract_document_name(rag_result)
            if doc_name:
                answer = f"Based on the {doc_name}, {answer.lower()}"
        
        # Add confidence disclaimer if needed
        avg_confidence = self._calculate_confidence(extraction_result, rag_result)
        if avg_confidence < confidence_threshold:
            answer += f"\n\n⚠️ **Note:** This answer has moderate confidence ({avg_confidence:.1f}). Please verify with original legal documents."
        
        # Add sources in simple format
        if include_sources:
            sources = self._format_sources(rag_result)
            if sources:
                answer += f"\n\n**Sources:** {sources}"
        
        # Add legal disclaimer
        answer += "\n\nIt is essential to note that while this analysis provides a general understanding, a full legal review is recommended for specific situations."
        
        return answer
    
    def _extract_document_name(self, rag_result: Dict) -> str:
        """Extract a clean document name from RAG results"""
        sources = rag_result.get('sources_used', [])
        if sources:
            # Clean up document name
            doc_name = sources[0].replace('_', ' ').replace('.pdf', '').title()
            return doc_name
        return ""
    
    def _calculate_confidence(self, extraction_result: Dict, rag_result: Dict) -> float:
        """Calculate average confidence score"""
        extraction_conf = extraction_result.get('confidence_score', 0)
        rag_conf = rag_result.get('confidence_level', 0)
        return (extraction_conf + rag_conf) / 2 if (extraction_conf or rag_conf) else 0.5
    
    def _format_sources(self, rag_result: Dict) -> str:
        """Format sources in a clean, simple way"""
        sources = rag_result.get('sources_used', [])
        if not sources:
            return "Unknown Section"
        
        # Clean up source names
        clean_sources = []
        for source in sources:
            clean_name = source.replace('_', ' ').replace('.pdf', '')
            clean_sources.append(clean_name)
        
        return ", ".join(clean_sources[:3])  # Limit to first 3 sources
    
    def _fallback_final_answer(self, query: str, extraction_result: Dict, rag_result: Dict, 
                             user_preferences: Dict, *args, **kwargs) -> str:
        """Generate fallback final answer in the desired format"""
        
        # Try to extract key information
        main_answer = rag_result.get('main_answer', 'Information not clearly specified in the available documents.')
        
        # Try to identify document
        doc_name = self._extract_document_name(rag_result) or "the relevant statute"
        
        # Create simple, direct answer
        answer = f"Based on {doc_name}, {main_answer}"
        
        # Add any specific details found
        if extraction_result.get('timeline'):
            answer += f" The specified timeframe is {extraction_result['timeline']}."
        
        if extraction_result.get('key_terms'):
            key_terms = extraction_result['key_terms'][:2]  # Limit to 2 key terms
            answer += f" This relates to {', '.join(key_terms).lower()}."
        
        # Add disclaimers
        confidence = self._calculate_confidence(extraction_result, rag_result)
        if confidence < 0.7:
            answer += f"\n\n⚠️ **Note:** This answer has moderate confidence ({confidence:.1f}). Please verify with original legal documents."
        
        sources = self._format_sources(rag_result)
        if sources:
            answer += f"\n\n**Sources:** {sources}"
        
        answer += "\n\nIt is essential to note that while this analysis provides a general understanding, a full legal review is recommended for specific situations."
        
        return answer

class DocumentManager:
    """Handles document lifecycle including deletion and cleanup"""
    
    def __init__(self, extraction_llm=None, rag_llm=None):
        self.extraction_llm = extraction_llm
        self.rag_llm = rag_llm
        self.base_paths = {
            'metadata': 'data/metadata',
            'processed': 'data/processed', 
            'chunks': 'data/chunks',
            'vectors': 'data/vectors',
            'cache': 'cache',
            'indexes': 'indexes'
        }
    
    def delete_document_completely(self, document_id: str, file_name: str) -> Dict:
        """Delete document from all systems"""
        results = {
            'document_id': document_id,
            'file_name': file_name,
            'deleted_components': [],
            'failed_components': [],
            'success': True
        }
        
        # 1. Delete metadata
        if self._delete_metadata(document_id):
            results['deleted_components'].append('metadata')
        else:
            results['failed_components'].append('metadata')
        
        # 2. Delete processed chunks
        if self._delete_chunks(document_id):
            results['deleted_components'].append('chunks')
        else:
            results['failed_components'].append('chunks')
        
        # 3. Delete vector data
        if self._delete_vectors(document_id):
            results['deleted_components'].append('vectors')
        else:
            results['failed_components'].append('vectors')
        
        # 4. Delete from LLM systems
        if self.extraction_llm and self.extraction_llm.delete_document_data(document_id, file_name):
            results['deleted_components'].append('extraction_llm')
        else:
            results['failed_components'].append('extraction_llm')
        
        if self.rag_llm and self.rag_llm.delete_document_from_rag(document_id, file_name):
            results['deleted_components'].append('rag_llm')
        else:
            results['failed_components'].append('rag_llm')
        
        # 5. Update indexes
        self._update_master_indexes(document_id)
        
        results['success'] = len(results['failed_components']) == 0
        return results
    
    def _delete_metadata(self, document_id: str) -> bool:
        """Delete document metadata"""
        try:
            import json
            
            # Delete individual metadata file
            metadata_file = f"{self.base_paths['metadata']}/{document_id}.json"
            if os.path.exists(metadata_file):
                os.remove(metadata_file)
            
            # Update master metadata index
            master_index = f"{self.base_paths['metadata']}/master_index.json"
            if os.path.exists(master_index):
                with open(master_index, 'r') as f:
                    data = json.load(f)
                
                # Remove document from index
                data['documents'] = [doc for doc in data.get('documents', []) 
                                   if doc.get('document_id') != document_id]
                
                with open(master_index, 'w') as f:
                    json.dump(data, f, indent=2)
            
            return True
        except Exception as e:
            print(f"Failed to delete metadata for {document_id}: {e}")
            return False
    
    def _delete_chunks(self, document_id: str) -> bool:
        """Delete document chunks"""
        try:
            chunks_dir = f"{self.base_paths['chunks']}/{document_id}"
            if os.path.exists(chunks_dir):
                shutil.rmtree(chunks_dir)
            
            # Delete individual chunk files
            chunk_files = glob.glob(f"{self.base_paths['chunks']}/{document_id}_*.json")
            for chunk_file in chunk_files:
                os.remove(chunk_file)
            
            return True
        except Exception as e:
            print(f"Failed to delete chunks for {document_id}: {e}")
            return False
    
    def _delete_vectors(self, document_id: str) -> bool:
        """Delete vector embeddings"""
        try:
            import json
            
            # Delete vector files
            vector_files = [
                f"{self.base_paths['vectors']}/{document_id}.pkl",
                f"{self.base_paths['vectors']}/{document_id}_embeddings.npy",
                f"{self.base_paths['vectors']}/{document_id}_index.faiss"
            ]
            
            for vector_file in vector_files:
                if os.path.exists(vector_file):
                    os.remove(vector_file)
            
            # Update vector index
            vector_index_file = f"{self.base_paths['vectors']}/vector_index.json"
            if os.path.exists(vector_index_file):
                with open(vector_index_file, 'r') as f:
                    index_data = json.load(f)
                
                # Remove document vectors from index
                index_data = [entry for entry in index_data 
                            if entry.get('document_id') != document_id]
                
                with open(vector_index_file, 'w') as f:
                    json.dump(index_data, f, indent=2)
            
            return True
        except Exception as e:
            print(f"Failed to delete vectors for {document_id}: {e}")
            return False
    
    def _update_master_indexes(self, document_id: str):
        """Update all master indexes after document deletion"""
        try:
            import json
            
            # Update document count and statistics
            stats_file = "data/document_stats.json"
            if os.path.exists(stats_file):
                with open(stats_file, 'r') as f:
                    stats = json.load(f)
                
                stats['total_documents'] = stats.get('total_documents', 1) - 1
                stats['last_updated'] = datetime.now().isoformat()
                
                if document_id in stats.get('processed_documents', {}):
                    del stats['processed_documents'][document_id]
                
                with open(stats_file, 'w') as f:
                    json.dump(stats, f, indent=2)
            
            print(f"Updated master indexes after deleting {document_id}")
        except Exception as e:
            print(f"Failed to update master indexes: {e}")


# Usage example - Add this to your main application

def handle_document_deletion(document_id: str, file_name: str, extraction_llm, rag_llm):
    """Handle complete document deletion"""
    
    # Initialize document manager
    doc_manager = DocumentManager(extraction_llm, rag_llm)
    
    # Perform complete deletion
    deletion_result = doc_manager.delete_document_completely(document_id, file_name)
    
    if deletion_result['success']:
        print(f"Successfully deleted document {file_name}")
        print(f"Deleted components: {', '.join(deletion_result['deleted_components'])}")
        return {"status": "success", "message": f"Document {file_name} deleted successfully"}
    else:
        print(f"Partial deletion of document {file_name}")
        print(f"Failed components: {', '.join(deletion_result['failed_components'])}")
        return {"status": "partial", "message": f"Some components of {file_name} could not be deleted"}
class ConversationManager:
    """Manages conversation threads and history"""
    
    def __init__(self, max_thread_age_hours=24, cleanup_interval_minutes=30):
        self.threads = {}  # Store conversation threads
        self.max_thread_age = timedelta(hours=max_thread_age_hours)
        self.cleanup_interval = cleanup_interval_minutes
        self.lock = threading.Lock()
        
        # Start cleanup thread
        self._start_cleanup_thread()
    
    def create_thread(self, user_id: str = None) -> str:
        """Create a new conversation thread"""
        thread_id = str(uuid.uuid4())
        
        with self.lock:
            self.threads[thread_id] = {
                'id': thread_id,
                'user_id': user_id,
                'created_at': datetime.now(),
                'last_activity': datetime.now(),
                'conversation_history': [],
                'context': {},  # Store any thread-specific context
                'metadata': {
                    'message_count': 0,
                    'documents_referenced': set(),
                    'search_queries': []
                }
            }
        
        print(f"Created new thread: {thread_id}")
        return thread_id
    
    def get_thread(self, thread_id: str) -> Optional[Dict]:
        """Get thread information"""
        with self.lock:
            return self.threads.get(thread_id)
    
    def update_thread_activity(self, thread_id: str):
        """Update thread last activity timestamp"""
        with self.lock:
            if thread_id in self.threads:
                self.threads[thread_id]['last_activity'] = datetime.now()
    
    def add_message_to_thread(self, thread_id: str, message_type: str, content: str, 
                             metadata: Dict = None):
        """Add a message to thread history"""
        if thread_id not in self.threads:
            return False
        
        with self.lock:
            thread = self.threads[thread_id]
            message = {
                'timestamp': datetime.now().isoformat(),
                'type': message_type,  # 'user', 'assistant', 'system'
                'content': content,
                'metadata': metadata or {}
            }
            
            thread['conversation_history'].append(message)
            thread['metadata']['message_count'] += 1
            self.update_thread_activity(thread_id)
        
        return True
    
    def get_conversation_context(self, thread_id: str, max_messages: int = 10) -> List[Dict]:
        """Get recent conversation context for a thread"""
        thread = self.get_thread(thread_id)
        if not thread:
            return []
        
        # Return last N messages for context
        return thread['conversation_history'][-max_messages:]
    
    def add_search_query(self, thread_id: str, query: str, results_count: int):
        """Track search queries in thread"""
        with self.lock:
            if thread_id in self.threads:
                self.threads[thread_id]['metadata']['search_queries'].append({
                    'query': query,
                    'timestamp': datetime.now().isoformat(),
                    'results_count': results_count
                })
    
    def cleanup_old_threads(self):
        """Remove threads older than max_thread_age"""
        current_time = datetime.now()
        threads_to_remove = []
        
        with self.lock:
            for thread_id, thread in self.threads.items():
                if current_time - thread['last_activity'] > self.max_thread_age:
                    threads_to_remove.append(thread_id)
            
            for thread_id in threads_to_remove:
                del self.threads[thread_id]
        
        if threads_to_remove:
            print(f"Cleaned up {len(threads_to_remove)} expired threads")
    
    def _start_cleanup_thread(self):
        """Start background thread for cleanup"""
        def cleanup_worker():
            while True:
                time.sleep(self.cleanup_interval * 60)  # Convert to seconds
                self.cleanup_old_threads()
        
        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()
    
    def get_thread_stats(self, thread_id: str) -> Dict:
        """Get statistics for a specific thread"""
        thread = self.get_thread(thread_id)
        if not thread:
            return {}
        
        return {
            'thread_id': thread_id,
            'created_at': thread['created_at'].isoformat(),
            'last_activity': thread['last_activity'].isoformat(),
            'message_count': thread['metadata']['message_count'],
            'search_count': len(thread['metadata']['search_queries']),
            'documents_referenced': len(thread['metadata']['documents_referenced'])
        }
    
# Add this new class for AI model integration
class AIModelClient:
    """Client for interfacing with various AI models"""
    
    def __init__(self):
        self.openai_client = None
        self.ollama_base_url = "http://localhost:11434"
        self.cohere_api_key = None
        self.huggingface_api_key = None
        
    def initialize_openai(self, api_key: str):
        """Initialize OpenAI client"""
        if OPENAI_AVAILABLE:
            try:
                import openai
                self.openai_client = openai.OpenAI(api_key=api_key)
                return True
            except Exception as e:
                print(f"Failed to initialize OpenAI: {e}")
                return False
        return False
    
    def set_cohere_key(self, api_key: str):
        """Set Cohere API key"""
        self.cohere_api_key = api_key
    
    def set_huggingface_key(self, api_key: str):
        """Set HuggingFace API key"""
        self.huggingface_api_key = api_key
    
    def generate_response(self, prompt: str, context: str, provider: str, model_name: str) -> Optional[str]:
        """Generate response using specified AI provider"""
        try:
            if provider == "openai":
                return self._generate_openai_response(prompt, context, model_name)
            elif provider == "ollama":
                return self._generate_ollama_response(prompt, context, model_name)
            elif provider == "cohere":
                return self._generate_cohere_response(prompt, context, model_name)
            elif provider == "huggingface":
                return self._generate_huggingface_response(prompt, context, model_name)
            else:
                return None
        except Exception as e:
            print(f"Error generating response with {provider}: {e}")
            return None
    
    def _generate_openai_response(self, prompt: str, context: str, model_name: str) -> Optional[str]:
        """Generate response using OpenAI"""
        if not self.openai_client:
            return None
        
        try:
            # Use appropriate model name, fallback to gpt-3.5-turbo if invalid
            valid_models = ["gpt-4", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-4o", "gpt-4o-mini"]
            if model_name not in valid_models:
                model_name = "gpt-3.5-turbo"
            
            response = self.openai_client.chat.completions.create(
                model=model_name,
                messages=[
                    {"role": "system", "content": prompt},
                    {"role": "user", "content": f"Based on this context: {context}"}
                ],
                max_tokens=500,
                temperature=0.7
            )
            return response.choices[0].message.content
        except Exception as e:
            print(f"OpenAI API error: {e}")
            return None
    
    def _generate_ollama_response(self, prompt: str, context: str, model_name: str) -> Optional[str]:
        """Generate response using Ollama"""
        try:
            url = f"{self.ollama_base_url}/api/generate"
            
            # Combine prompt and context
            full_prompt = f"{prompt}\n\nContext: {context}"
            
            payload = {
                "model": model_name,
                "prompt": full_prompt,
                "stream": False,
                "options": {
                    "temperature": 0.7,
                    "num_predict": 500
                }
            }
            
            response = requests.post(url, json=payload, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            return result.get('response', '')
            
        except requests.exceptions.RequestException as e:
            print(f"Ollama API error: {e}")
            return None
    
    def _generate_cohere_response(self, prompt: str, context: str, model_name: str) -> Optional[str]:
        """Generate response using Cohere"""
        if not self.cohere_api_key:
            return None
        
        try:
            url = "https://api.cohere.ai/v1/generate"
            
            headers = {
                "Authorization": f"Bearer {self.cohere_api_key}",
                "Content-Type": "application/json"
            }
            
            # Combine prompt and context
            full_prompt = f"{prompt}\n\nContext: {context}"
            
            payload = {
                "model": model_name or "command",
                "prompt": full_prompt,
                "max_tokens": 500,
                "temperature": 0.7
            }
            
            response = requests.post(url, json=payload, headers=headers, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            return result.get('generations', [{}])[0].get('text', '')
            
        except requests.exceptions.RequestException as e:
            print(f"Cohere API error: {e}")
            return None
    
    def _generate_huggingface_response(self, prompt: str, context: str, model_name: str) -> Optional[str]:
        """Generate response using HuggingFace Inference API"""
        if not self.huggingface_api_key:
            return None
        
        try:
            # Use a default model if none specified
            if not model_name:
                model_name = "microsoft/DialoGPT-medium"
            
            url = f"https://api-inference.huggingface.co/models/{model_name}"
            
            headers = {
                "Authorization": f"Bearer {self.huggingface_api_key}",
                "Content-Type": "application/json"
            }
            
            # Combine prompt and context
            full_prompt = f"{prompt}\n\nContext: {context}"
            
            payload = {
                "inputs": full_prompt,
                "parameters": {
                    "max_length": 500,
                    "temperature": 0.7,
                    "do_sample": True
                }
            }
            
            response = requests.post(url, json=payload, headers=headers, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            if isinstance(result, list) and len(result) > 0:
                return result[0].get('generated_text', '')
            return ''
            
        except requests.exceptions.RequestException as e:
            print(f"HuggingFace API error: {e}")
            return None

# Initialize global AI client
ai_client = AIModelClient()

# Modified function to generate AI responses
def generate_ai_response(query: str, search_results: List[Dict], custom_prompt: str, 
                        ai_provider: str, model_name: str) -> str:
    """Generate AI response using search results and custom prompt"""
    
    # Prepare context from search results
    context_parts = []
    for i, result in enumerate(search_results[:3], 1):  # Use top 3 results
        section = result.get('section', 'Unknown Section')
        subsection = result.get('subsection', '')
        content = result.get('content', '')
        relevance = result.get('relevanceScore', 0)
        
        context_part = f"""
Source {i} (Relevance: {relevance:.2f}):
Section: {section} {subsection}
Content: {content[:500]}...
"""
        context_parts.append(context_part)
    
    # Combine all context
    full_context = "\n".join(context_parts)
    
    # Create the full prompt
    full_prompt = f"""{custom_prompt}

User Question: {query}

Please provide a direct, accurate answer based on the provided legal document context below. If the context doesn't contain enough information to answer the question, say so clearly."""
    
    # Generate response using AI model
    ai_response = ai_client.generate_response(
        prompt=full_prompt,
        context=full_context,
        provider=ai_provider,
        model_name=model_name
    )
    
    # Fallback if AI generation fails
    if not ai_response:
        return generate_fallback_response(query, search_results)
    
    return ai_response

def generate_fallback_response(query: str, results: List[Dict]) -> str:
    """Generate fallback response if AI model fails"""
    if not results:
        return f"I couldn't find relevant information about '{query}' in the processed documents."
    
    best_result = results[0]
    section = best_result.get('section', 'the relevant section')
    content = best_result.get('content', '')
    
    # Extract most relevant sentence
    sentences = content.split('.')
    for sentence in sentences:
        if len(sentence.strip()) > 30 and any(term in sentence.lower() for term in query.lower().split()):
            return f"Based on {section}: {sentence.strip()}."
    
    return f"According to {section}: {content[:200]}..."
# Session token management for frontend
class SessionTokenManager:
    """Manage session tokens for frontend clients"""
    
    def __init__(self):
        self.sessions = {}
        self.lock = threading.Lock()
    
    def create_session(self, user_id: str = None) -> str:
        """Create a new session token"""
        session_token = str(uuid.uuid4())
        thread_id = conversation_manager.create_thread(user_id)
        
        with self.lock:
            self.sessions[session_token] = {
                'thread_id': thread_id,
                'user_id': user_id,
                'created_at': datetime.now(),
                'last_used': datetime.now()
            }
        
        return session_token
    
    def get_thread_id_from_token(self, session_token: str) -> Optional[str]:
        """Get thread ID from session token"""
        with self.lock:
            session = self.sessions.get(session_token)
            if session:
                session['last_used'] = datetime.now()
                return session['thread_id']
        return None
    
    def cleanup_expired_sessions(self):
        """Remove expired sessions"""
        current_time = datetime.now()
        expired_tokens = []
        
        with self.lock:
            for token, session in self.sessions.items():
                if current_time - session['last_used'] > timedelta(hours=24):
                    expired_tokens.append(token)
            
            for token in expired_tokens:
                del self.sessions[token]

# Global session manager
session_manager = SessionTokenManager()

# Initialize Flask app early (before route decorators)
app = Flask(__name__)
CORS(app)

# Configure upload settings
UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'pdf', 'txt', 'docx', 'xlsx'}
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Create upload folder if it doesn't exist
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

@app.route('/api/session/create', methods=['POST'])
def create_session():
    """Create a new session with token"""
    data = request.get_json() or {}
    user_id = data.get('user_id')
    
    session_token = session_manager.create_session(user_id)
    
    return jsonify({
        'status': 'success',
        'session_token': session_token,
        'message': 'New session created'
    })

@app.route('/api/chat', methods=['POST'])
def chat_with_context():
    """Main chat endpoint that maintains conversation context"""
    data = request.get_json()
    query = data.get('query', '')
    session_token = data.get('session_token', '')
    top_k = data.get('top_k', 5)
    
    if not query:
        return jsonify({
            'status': 'error',
            'message': 'No query provided'
        }), 400
    
    if not session_token:
        return jsonify({
            'status': 'error',
            'message': 'No session_token provided'
        }), 400
    
    # Get thread ID from session token
    thread_id = session_manager.get_thread_id_from_token(session_token)
    if not thread_id:
        return jsonify({
            'status': 'error',
            'message': 'Invalid or expired session token'
        }), 401
    
    if not enhanced_processor or not enhanced_processor.chunks:
        return jsonify({
            'status': 'error',
            'message': 'No documents processed yet. Please upload and process documents first.'
        }), 400
    
    try:
        # Add user message to conversation history
        conversation_manager.add_message_to_thread(
            thread_id, 'user', query, {'timestamp': datetime.now().isoformat()}
        )
        
        # Get conversation context
        context = conversation_manager.get_conversation_context(thread_id, max_messages=10)
        
        # Perform contextual search
        search_result = enhanced_processor.search_with_context(query, thread_id, top_k)
        
        # Generate response
        response = generate_contextual_response(query, search_result, context)
        
        # Add assistant response to conversation history
        conversation_manager.add_message_to_thread(
            thread_id, 'assistant', response, {
                'search_metadata': search_result,
                'timestamp': datetime.now().isoformat()
            }
        )
        
        return jsonify({
            'status': 'success',
            'response': response,
            'session_token': session_token,  # Return token for next request
            'conversation_metadata': {
                'thread_id': thread_id,
                'message_count': len(context) + 1,
                'context_used': search_result.get('context_used', False),
                'enhanced_query': search_result.get('enhanced_query', query)
            },
            'sources': format_sources(search_result['results'])
        })
        
    except Exception as e:
        print(f"Error in chat endpoint: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Chat request failed: {str(e)}'
        }), 500

def generate_contextual_response(query: str, search_result: Dict, context: List[Dict]) -> str:
    """Generate response considering conversation context in Q&A format"""
    results = search_result['results']

    if not results:
        return f"**Q:** {query}\n\n**A:** I couldn't find specific information about this question in the processed legal documents."

    # Check if this is a follow-up question
    is_followup = len(context) > 1 and search_result.get('context_used', False)

    # Get the most relevant result
    best_result = results[0]
    content = best_result['content']

    # Extract specific answer
    specific_answer = extract_specific_answer(content, query)

    if specific_answer and "not found" not in specific_answer.lower():
        if is_followup:
            # More conversational response for follow-ups
            answer = f"Based on our conversation and the documents, {specific_answer}"
        else:
            answer = specific_answer
        return f"**Q:** {query}\n\n**A:** {answer}"
    else:
        # Fallback response
        section = best_result.get('section', '')
        if is_followup:
            answer = f"Based on our conversation and according to {section}, {content[:150]}..."
        else:
            answer = f"According to {section}, {content[:150]}..."
        return f"**Q:** {query}\n\n**A:** {answer}"

# Thread context helpers
def get_related_queries_from_context(context: List[Dict]) -> List[str]:
    """Extract related queries from conversation context"""
    queries = []
    for message in context:
        if message['type'] == 'user' and message.get('metadata', {}).get('search_query'):
            queries.append(message['content'])
    return queries[-3:]  # Last 3 queries

def extract_entities_from_context(context: List[Dict]) -> List[str]:
    """Extract legal entities/topics from conversation context"""
    entities = []
    legal_entity_patterns = [
        r'\b\d+\.\d+\b',  # Section numbers like 320.696
        r'\bSection\s+\d+\b',
        r'\bwarranty\b',
        r'\breimbursement\b',
        r'\bdealer\b',
        r'\blicensee\b'
    ]
    
    for message in context:
        if message['type'] in ['user', 'assistant']:
            content = message['content']
            for pattern in legal_entity_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                entities.extend(matches)
    
    return list(set(entities))  # Remove duplicates

# Usage example for frontend JavaScript:
"""
// Frontend JavaScript example

class LegalChatClient {
    constructor(baseURL = 'http://localhost:5000/api') {
        this.baseURL = baseURL;
        this.sessionToken = null;
    }
    
    async createSession(userId = null) {
        const response = await fetch(`${this.baseURL}/session/create`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ user_id: userId })
        });
        
        const data = await response.json();
        if (data.status === 'success') {
            this.sessionToken = data.session_token;
            return this.sessionToken;
        }
        throw new Error(data.message);
    }
    
    async chat(query) {
        if (!this.sessionToken) {
            await this.createSession();
        }
        
        const response = await fetch(`${this.baseURL}/chat`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                query: query,
                session_token: this.sessionToken
            })
        });
        
        const data = await response.json();
        
        if (data.status === 'success') {
            // Update session token if provided (for token refresh)
            if (data.session_token) {
                this.sessionToken = data.session_token;
            }
            return data;
        }
        
        throw new Error(data.message);
    }
    
    async getConversationHistory() {
        if (!this.sessionToken) {
            throw new Error('No active session');
        }
        
        const threadId = await this.getThreadId();
        const response = await fetch(`${this.baseURL}/thread/${threadId}/history`);
        return await response.json();
    }
    
    async getThreadId() {
        // This would need to be implemented based on your session management
        // For now, you could store it or get it from the session
        return this.threadId;
    }
}

// Usage:
const chatClient = new LegalChatClient();

// Start conversation
await chatClient.createSession();
const response1 = await chatClient.chat("What is the warranty notice requirement?");
const response2 = await chatClient.chat("How many days notice is required?"); // This will use context
"""

# Additional thread management endpoints
@app.route('/api/thread/<thread_id>/clear', methods=['POST'])
def clear_thread(thread_id: str):
    """Clear conversation history for a specific thread"""
    thread = conversation_manager.get_thread(thread_id)
    
    if not thread:
        return jsonify({
            'status': 'error',
            'message': 'Thread not found'
        }), 404
    
    with conversation_manager.lock:
        conversation_manager.threads[thread_id]['conversation_history'] = []
        conversation_manager.threads[thread_id]['metadata']['message_count'] = 0
        conversation_manager.threads[thread_id]['metadata']['search_queries'] = []
    
    return jsonify({
        'status': 'success',
        'message': 'Thread history cleared'
    })

@app.route('/api/threads/active', methods=['GET'])
def get_active_threads():
    """Get list of active threads"""
    active_threads = []
    
    with conversation_manager.lock:
        for thread_id, thread in conversation_manager.threads.items():
            if datetime.now() - thread['last_activity'] < timedelta(hours=1):  # Active in last hour
                active_threads.append({
                    'thread_id': thread_id,
                    'last_activity': thread['last_activity'].isoformat(),
                    'message_count': thread['metadata']['message_count']
                })
    
    return jsonify({
        'status': 'success',
        'active_threads': active_threads
    })

# Context-aware response generation
def generate_contextual_legal_response(query: str, results: List[Dict], context: List[Dict]) -> str:
    """Generate response that considers conversation context"""
    if not results:
        return f"I couldn't find information about '{query}' in the processed documents."
    
    # Check for follow-up patterns
    previous_queries = [msg['content'] for msg in context if msg['type'] == 'user']
    
    if len(previous_queries) > 1:
        # This might be a follow-up question
        last_query = previous_queries[-2] if len(previous_queries) > 1 else ""
        
        # Check for pronouns or references that need context
        if any(word in query.lower() for word in ['that', 'this', 'it', 'they', 'same']):
            response = f"Continuing from your previous question about {last_query.lower()}: "
        else:
            response = ""
    else:
        response = ""
    
    # Generate core answer
    best_result = results[0]
    specific_answer = extract_specific_answer(best_result['content'], query)
    
    if specific_answer and "not found" not in specific_answer.lower():
        response += specific_answer
    else:
        section = best_result.get('section', 'the relevant section')
        response += f"According to {section}, {best_result['content'][:150]}..."
    
    return response
class LegalDocumentProcessor:
    def __init__(self, embedding_model=None, model_name=None, openai_api_key=None, **kwargs):
        """Initialize the legal document processor with proper error handling"""
        self.doc_converter = None
        self.initialized = False
        self.chunks = []  # Store processed chunks in memory
        self.processed_files = []
        self.embedding_model = embedding_model or 'sentence_transformers'
        self.model_name = model_name or 'BAAI/bge-large-en-v1.5'
        
        # Initialize search components
        self.bm25 = None
        self.tfidf_vectorizer = None
        self.tfidf_matrix = None
        self.openai_client = None
        
        # Store any additional kwargs for future use
        self.config = kwargs
        
        # Initialize OpenAI client if API key provided
        if openai_api_key and OPENAI_AVAILABLE:
            try:
                self.openai_client = openai.OpenAI(api_key=openai_api_key)
                print("✅ OpenAI client initialized")
            except Exception as e:
                print(f"⚠️ Failed to initialize OpenAI client: {e}")
        
        if not DOCLING_AVAILABLE:
            print("❌ Cannot initialize: Docling not available")
            return
        
        try:
            print("🔄 Initializing Docling document converter...")
            
            # Try the new configuration approach
            try:
                pdf_options = PdfPipelineOptions()
                pdf_options.do_ocr = True
                pdf_options.do_table_structure = True
                
                self.doc_converter = DocumentConverter(
                    format_options={
                        InputFormat.PDF: pdf_options,
                    }
                )
                print("✅ Docling converter initialized with advanced options")
                
            except Exception as e:
                print(f"⚠️ Advanced config failed ({e}), trying simple config...")
                # Fallback to simple initialization
                self.doc_converter = DocumentConverter()
                print("✅ Docling converter initialized with basic options")
            
            self.initialized = True
            print("✅ Legal Document Processor initialized successfully")
            
        except Exception as e:
            print(f"❌ Failed to initialize document converter: {e}")
            print(f"   Error type: {type(e).__name__}")
            print("   Traceback:")
            traceback.print_exc()
            self.initialized = False
    def save_chunks_to_json(self, chunks, file_path):
        """Save chunks to JSON file"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(chunks, f, indent=2, ensure_ascii=False)
            print(f"✅ Chunks saved to {file_path}")
        except Exception as e:
            print(f"❌ Error saving chunks to JSON: {e}")

    def is_initialized(self) -> bool:
        """Check if the processor is properly initialized"""
        return self.initialized and self.doc_converter is not None

    def _initialize_search_indices(self):
        """Initialize search indices after processing documents"""
        if not self.chunks:
            return
        
        print("🔄 Initializing search indices...")
        
        # Prepare documents for indexing
        documents = [chunk['content'] for chunk in self.chunks]
        
        # Initialize BM25 if available
        if BM25_AVAILABLE:
            try:
                tokenized_docs = [doc.lower().split() for doc in documents]
                self.bm25 = BM25Okapi(tokenized_docs)
                print("✅ BM25 index initialized")
            except Exception as e:
                print(f"⚠️ BM25 initialization failed: {e}")
        
        # Initialize TF-IDF as fallback
        if SKLEARN_AVAILABLE:
            try:
                self.tfidf_vectorizer = TfidfVectorizer(
                    max_features=1000,
                    stop_words='english',
                    ngram_range=(1, 2)
                )
                self.tfidf_matrix = self.tfidf_vectorizer.fit_transform(documents)
                print("✅ TF-IDF index initialized")
            except Exception as e:
                print(f"⚠️ TF-IDF initialization failed: {e}")

    def process_legal_document(self, file_path: str) -> Dict[str, Any]:
        """Main pipeline to process legal documents"""
        if not self.is_initialized():
            return {
                'status': 'error',
                'message': 'Processor not initialized',
                'chunks': 0
            }
        
        try:
            print(f"📄 Processing: {file_path}")
            
            print("Step 1: Processing with Docling...")
            docling_result = self.process_document_with_docling(file_path)
            print(f"   Docling result: {docling_result}")
            if not docling_result:
                return {
                    'status': 'error',
                    'message': 'Failed to process document with Docling',
                    'chunks': 0
                }
            
            print("Step 2: Extracting legal sections...")
            sections = self.extract_legal_sections(docling_result['content'])
            print(f"   Extracted {len(sections)} sections")
            if not sections:
                return {
                    'status': 'error',
                    'message': 'No sections extracted from document',
                    'chunks': 0
                }
            
            print("Step 3: Creating semantic chunks...")
            chunks = self.create_legal_chunks(sections, file_path)
            print(f"   Created {len(chunks)} chunks")
            print("Step 4: Creating hybrid embeddings...")
            chunks_with_embeddings = self.create_hybrid_embeddings(chunks)
            print(f"   Created {len(chunks_with_embeddings)} chunks with embeddings")
            # Store chunks in memory
            self.chunks.extend(chunks_with_embeddings)
            self.processed_files.append(file_path)
            
            # Initialize search indices after adding new chunks
            self._initialize_search_indices()
            
            print(f"✅ Processing complete! Created {len(chunks_with_embeddings)} chunks")
            
            return {
                'status': 'success',
                'message': f'Successfully processed {len(chunks_with_embeddings)} chunks',
                'chunks': len(chunks_with_embeddings)
            }
            
        except Exception as e:
            print(f"❌ Error processing document: {e}")
            traceback.print_exc()
            return {
                'status': 'error',
                'message': f'Processing failed: {str(e)}',
                'chunks': 0
            }
    

    def process_xlsx_document(self, file_path: str) -> Optional[Dict[str, Any]]:
        """Process XLSX document using pandas or fallback method"""

        # Try pandas first if available
        if PANDAS_AVAILABLE:
            try:
                print(f"📊 Processing XLSX document with pandas: {file_path}")

                # Read all sheets from the Excel file
                excel_file = pd.ExcelFile(file_path)
                all_content = []

                for sheet_name in excel_file.sheet_names:
                    print(f"   📋 Processing sheet: {sheet_name}")
                    df = pd.read_excel(file_path, sheet_name=sheet_name)

                    # Convert DataFrame to readable text
                    sheet_content = f"## Sheet: {sheet_name}\n\n"

                    # Add column headers
                    if not df.empty:
                        headers = " | ".join(str(col) for col in df.columns)
                        sheet_content += f"**Columns:** {headers}\n\n"

                        # Add rows as text
                        for idx, row in df.iterrows():
                            row_text = " | ".join(str(val) if pd.notna(val) else "" for val in row.values)
                            sheet_content += f"Row {idx + 1}: {row_text}\n"

                    all_content.append(sheet_content)

                # Combine all sheets
                markdown_content = "\n\n".join(all_content)

                if markdown_content and len(markdown_content.strip()) > 0:
                    print(f"✅ XLSX processing successful - {len(markdown_content)} characters")
                    return {
                        'content': markdown_content,
                        'metadata': {
                            'file_path': file_path,
                            'sheets': excel_file.sheet_names,
                            'processing_method': 'pandas_xlsx'
                        }
                    }
                else:
                    print("⚠️ No content extracted from XLSX")
                    return None

            except Exception as e:
                print(f"❌ XLSX processing with pandas failed: {e}")
                # Fall through to basic fallback

        # Fallback: Basic XLSX processing without pandas
        try:
            print(f"📊 Processing XLSX document with basic method: {file_path}")

            # Simple fallback - just indicate the file type and basic info
            import os
            file_size = os.path.getsize(file_path)
            file_name = os.path.basename(file_path)

            fallback_content = f"""# Excel Document: {file_name}

            **File Type:** Microsoft Excel (.xlsx)
            **File Size:** {file_size} bytes
            **Processing Method:** Basic fallback (pandas not available)

            **Note:** This is an Excel spreadsheet file. For full content extraction, please ensure pandas and openpyxl are installed.

            The file has been uploaded and can be processed once the required libraries are available.
            """

            print(f"✅ Basic XLSX processing completed")
            return {
                'content': fallback_content,
                'metadata': {
                    'file_path': file_path,
                    'file_size': file_size,
                    'processing_method': 'basic_fallback'
                }
            }

        except Exception as e:
            print(f"❌ Basic XLSX processing failed: {e}")
            return None

    def process_document_with_docling(self, file_path: str) -> Optional[Dict[str, Any]]:
        """Process legal document using Docling for structure preservation"""

        # Check if this is an XLSX file and handle it separately
        if file_path.lower().endswith('.xlsx'):
            return self.process_xlsx_document(file_path)

        try:
            print(f"📄 Converting document: {file_path}")
            result = self.doc_converter.convert(file_path)
            
            # Get the markdown content
            markdown_content = result.document.export_to_markdown()
            print(f"markdown_content: {markdown_content}")
            if not markdown_content or len(markdown_content.strip()) == 0:
                print("⚠️ Warning: Document converted but no content extracted")
                return None
            
            print(f"✅ Document converted successfully. Content length: {len(markdown_content)} characters")
            
            return {
                'content': markdown_content,
                'structure': result.document.structure if hasattr(result.document, 'structure') else None,
                'metadata': result.document.metadata if hasattr(result.document, 'metadata') else {}
            }
            
        except Exception as e:
            print(f"❌ Error processing document with Docling: {e}")
            print(f"   Error type: {type(e).__name__}")
            
            # Try alternative approach if main method fails
            try:
                print("🔄 Attempting alternative conversion...")
                simple_converter = DocumentConverter()
                result = simple_converter.convert(file_path)
                markdown_content = result.document.export_to_markdown()
                
                if markdown_content and len(markdown_content.strip()) > 0:
                    print("✅ Alternative conversion successful")
                    return {
                        'content': markdown_content,
                        'structure': None,
                        'metadata': {}
                    }
            except Exception as e2:
                print(f"❌ Alternative conversion also failed: {e2}")
            
            return None
    def extract_legal_sections(self, markdown_content: str) -> List[Dict[str, str]]:
        """Extract sections and subsections from legal document markdown"""
        if not markdown_content:
            print("❌ No markdown content to extract sections from")
            return []
        
        print(f"📝 Extracting sections from {len(markdown_content)} characters of content")
        
        sections = []
        current_section = ""
        current_subsection = ""
        current_content = ""
        
        lines = markdown_content.split('\n')
        print(f"📄 Processing {len(lines)} lines")
        
        for i, line in enumerate(lines):
            line_stripped = line.strip()
            
            # Skip empty lines in section detection but include in content
            if not line_stripped and not current_content:
                continue
            
            # Detect main sections (like "320.696 Warranty responsibility" or similar patterns)
            section_patterns = [
                r'^\d+\.\d+',  # 320.696
                r'^§\s*\d+',   # § 123
                r'^Section\s+\d+',  # Section 123
                r'^SECTION\s+\d+',  # SECTION 123
                r'^Art\.\s*\d+',    # Art. 123
                r'^Chapter\s+\d+',  # Chapter 123
            ]
            
            is_main_section = any(re.match(pattern, line_stripped) for pattern in section_patterns)
            
            if is_main_section:
                # Save previous section if it has content
                if current_content.strip():
                    sections.append({
                        'section': current_section,
                        'subsection': current_subsection,
                        'content': current_content.strip()
                    })
                
                current_section = line_stripped
                current_subsection = ""
                current_content = ""
                continue
            
            # Detect subsections (like "(1)", "(a)", etc.)
            subsection_patterns = [
                r'^\([a-zA-Z0-9]+\)',  # (1), (a), (A)
                r'^\([ivxlcdm]+\)',    # (i), (ii), (iii) - roman numerals
                r'^\d+\.',             # 1., 2., 3.
                r'^[a-zA-Z]\.',        # a., b., A., B.
            ]
            
            is_subsection = any(re.match(pattern, line_stripped) for pattern in subsection_patterns)
            
            if is_subsection and current_section:
                # Save previous subsection if it has content
                if current_content.strip() and current_subsection:
                    sections.append({
                        'section': current_section,
                        'subsection': current_subsection,
                        'content': current_content.strip()
                    })
                
                current_subsection = line_stripped
                current_content = ""
                continue
            
            # Add line to current content
            current_content += line + '\n'
        
        # Add the last section/subsection
        if current_content.strip():
            sections.append({
                'section': current_section,
                'subsection': current_subsection,
                'content': current_content.strip()
            })
        
        print(f"✅ Extracted {len(sections)} sections from document")
        
        return sections
    def create_legal_chunks(self, sections: List[Dict[str, str]], file_path: str) -> List[Dict]:
        """Create chunks from legal sections with better context"""
        if not sections:
            print("❌ No sections provided for chunk creation")
            return []
        
        chunks = []
        file_name = os.path.basename(file_path)
        
        for i, section in enumerate(sections):
            # Create meaningful chunk content, ensuring each chunk has relevant context
            chunk_content = ""
            
            # Add section information to chunk content
            if section['section']:
                chunk_content += f"Section: {section['section']}\n"
            
            if section['subsection']:
                chunk_content += f"Subsection: {section['subsection']}\n"
            
            if section['content']:
                chunk_content += f"\nContent:\n{section['content']}"
            
            if chunk_content.strip():
                chunks.append({
                    'id': f"{file_name}_chunk_{i+1}",
                    'content': chunk_content.strip(),
                    'section': section['section'],
                    'subsection': section['subsection'],
                    'file_name': file_name,
                    'dense_embedding': None,  # Will be populated if OpenAI is available
                    'metadata': {
                        'chunk_index': i,
                        'section_title': section['section'],
                        'subsection_title': section['subsection'],
                        'file_name': file_name
                    }
                })
        
        print(f"✅ Created {len(chunks)} legal chunks with context")
        return chunks

   
    def create_hybrid_embeddings(self, chunks: List[Dict]) -> List[Dict]:
        """Create hybrid embeddings for chunks"""
        if not chunks:
            return []
        
        print("🔄 Creating embeddings for chunks...")
        
        # Create dense embeddings using OpenAI if available
        if self.openai_client and OPENAI_AVAILABLE:
            try:
                print("Creating OpenAI embeddings...")
                for chunk in chunks:
                    response = self.openai_client.embeddings.create(
                        input=chunk['content'],
                        model="text-embedding-3-large"
                    )
                    chunk['dense_embedding'] = response.data[0].embedding
                print("✅ OpenAI embeddings created")
            except Exception as e:
                print(f"⚠️ OpenAI embedding creation failed: {e}")
                # Add placeholder embeddings
                for chunk in chunks:
                    chunk['dense_embedding'] = [0.0] * 384
        else:
            # Add placeholder embeddings
            for chunk in chunks:
                chunk['dense_embedding'] = [0.0] * 384
        
        print(f"✅ Created embeddings for {len(chunks)} chunks")
        return chunks
    def hybrid_search(self, query: str, top_k: int = 5) -> List[Dict]:
        """Perform hybrid search using both dense and sparse retrieval"""
        if not self.chunks:
            print("❌ No chunks available for search")
            return []
        
        print(f"🔍 Performing hybrid search for: '{query}'")
        
        # Initialize scores list
        combined_scores = []
        
        # BM25 search (sparse) if available
        bm25_scores = []
        if self.bm25 and BM25_AVAILABLE:
            try:
                query_tokens = query.lower().split()
                bm25_scores = self.bm25.get_scores(query_tokens)
                print("✅ BM25 search completed")
            except Exception as e:
                print(f"⚠️ BM25 search failed: {e}")
                bm25_scores = []
        
        # TF-IDF search as fallback - FIXED: Check list length instead of truthiness
        tfidf_scores = []
        if len(bm25_scores) == 0 and self.tfidf_vectorizer and SKLEARN_AVAILABLE:
            try:
                query_vector = self.tfidf_vectorizer.transform([query])
                tfidf_scores = cosine_similarity(query_vector, self.tfidf_matrix)[0]
                print("✅ TF-IDF search completed")
            except Exception as e:
                print(f"⚠️ TF-IDF search failed: {e}")
                tfidf_scores = []
        
        # Dense similarity search if OpenAI embeddings available
        dense_similarities = []
        if self.openai_client and OPENAI_AVAILABLE:
            try:
                query_embedding = self.openai_client.embeddings.create(
                    input=query,
                    model="text-embedding-3-large"
                ).data[0].embedding
                
                for chunk in self.chunks:
                    if chunk.get('dense_embedding') and len(chunk['dense_embedding']) > 1:
                        similarity = cosine_similarity(
                            [query_embedding], 
                            [chunk['dense_embedding']]
                        )[0][0]
                        dense_similarities.append(similarity)
                    else:
                        dense_similarities.append(0.0)
                print("✅ Dense similarity search completed")
            except Exception as e:
                print(f"⚠️ Dense search failed: {e}")
                dense_similarities = []
        
        # Simple keyword search as ultimate fallback
        if len(bm25_scores) == 0 and len(tfidf_scores) == 0 and len(dense_similarities) == 0:
            print("📝 Using simple keyword search fallback")
            query_lower = query.lower()
            for chunk in self.chunks:
                content_lower = chunk['content'].lower()
                if query_lower in content_lower:
                    score = content_lower.count(query_lower) / len(content_lower.split())
                    combined_scores.append((min(score * 10, 1.0), chunk))
                else:
                    combined_scores.append((0.0, chunk))
        else:
            # Combine scores (weighted average)
            for i, chunk in enumerate(self.chunks):
                # Get individual scores
                sparse_score = 0.0
                if len(bm25_scores) > 0:
                    # Normalize BM25 scores
                    max_bm25 = max(bm25_scores) if max(bm25_scores) > 0 else 1.0
                    sparse_score = bm25_scores[i] / max_bm25
                elif len(tfidf_scores) > 0:
                    sparse_score = tfidf_scores[i]
                
                dense_score = dense_similarities[i] if len(dense_similarities) > 0 else 0.0
                
                # Weight: 0.6 dense, 0.4 sparse for legal docs (if both available)
                if len(dense_similarities) > 0 and (len(bm25_scores) > 0 or len(tfidf_scores) > 0):
                    combined_score = 0.6 * dense_score + 0.4 * sparse_score
                elif len(dense_similarities) > 0:
                    combined_score = dense_score
                elif len(bm25_scores) > 0 or len(tfidf_scores) > 0:
                    combined_score = sparse_score
                else:
                    combined_score = 0.0
                
                combined_scores.append((combined_score, chunk))
        
        # Sort and return top_k results
        combined_scores.sort(key=lambda x: x[0], reverse=True)
        
        # Add relevance scores to results
        results = []
        for score, chunk in combined_scores[:top_k]:
            result_chunk = chunk.copy()
            result_chunk['relevanceScore'] = score
            results.append(result_chunk)
        
        print(f"✅ Hybrid search completed, returning {len(results)} results")
        return results
    def search_chunks(self, query: str, top_k: int = 5) -> List[Dict]:
        """Unified search method that uses hybrid search"""
        return self.hybrid_search(query, top_k)

    def update_config(self, **kwargs):
        """Update processor configuration"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
            else:
                self.config[key] = value
        print(f"✅ Configuration updated: {kwargs}")

    def get_stats(self):
        """Get processor statistics"""
        return {
            'chunks_loaded': len(self.chunks),
            'files_processed': len(self.processed_files),
            'initialized': self.initialized,
            'bm25_available': self.bm25 is not None,
            'tfidf_available': self.tfidf_vectorizer is not None,
            'openai_available': self.openai_client is not None
        }

    def get_config(self):
        """Get current configuration"""
        return {
            'embedding_model': self.embedding_model,
            'model_name': self.model_name,
            'initialized': self.initialized,
            'search_capabilities': {
                'bm25': BM25_AVAILABLE and self.bm25 is not None,
                'tfidf': SKLEARN_AVAILABLE and self.tfidf_vectorizer is not None,
                'dense_embeddings': OPENAI_AVAILABLE and self.openai_client is not None
            },
            **self.config
        }

    def clear_data(self):
        # """Clear all processed data"""
        self.chunks = []
        self.processed_files = []
        self.bm25 = None
        self.tfidf_vectorizer = None
        self.tfidf_matrix = None
        print("✅ All data cleared")



class EnhancedLegalDocumentProcessor(LegalDocumentProcessor):
    """Enhanced processor with multi-LLM architecture and improved search capabilities"""
    
    def __init__(self, embedding_model=None, model_name=None, openai_api_key=None, groq_api_key=None, **kwargs):
        super().__init__(embedding_model, model_name, openai_api_key, **kwargs)
        
        # Initialize document metadata storage
        self.document_metadata = {}
        self.document_chunks_by_file = {}
        self.document_registry = {}
        
        # Initialize specialized LLMs
        self.extraction_llm = ExtractionLLM()
        self.rag_llm = RAGBasedLLM()
        self.final_answer_llm = FinalAnswerGeneratorLLM()
       
        # Initialize LLMs with API keys (prioritize Groq)
        if groq_api_key:
            self.extraction_llm.initialize_groq(groq_api_key)
            self.rag_llm.initialize_groq(groq_api_key)
            self.final_answer_llm.initialize_groq(groq_api_key)
            print("✅ Groq clients initialized for all LLMs")
        elif openai_api_key:
            self.extraction_llm.initialize_openai(openai_api_key)
            self.rag_llm.initialize_openai(openai_api_key)
            self.final_answer_llm.initialize_openai(openai_api_key)
            print("✅ OpenAI clients initialized for all LLMs")
        
        print("✅ Enhanced Legal Document Processor with multi-LLM architecture initialized")
        
        # Initialize embedding model
        self._initialize_embedding_model()
    
    def _initialize_embedding_model(self):
        """Initialize embedding model if not already done"""
        try:
            if hasattr(self, 'embedding_model') and self.embedding_model is not None:
                return
            
            try:
                from langchain.embeddings import HuggingFaceEmbeddings
                self.embedding_model = HuggingFaceEmbeddings(
                    model_name="sentence-transformers/all-MiniLM-L6-v2"
                )
                print("✅ HuggingFace embeddings initialized")
                return
            except ImportError:
                pass
            
            # Fallback: simple text-based "embeddings"
            class SimpleEmbeddings:
                def embed_documents(self, texts):
                    return [[1.0] * 384 for _ in texts]
                
                def embed_query(self, text):
                    return [1.0] * 384
            
            self.embedding_model = SimpleEmbeddings()
            print("⚠️ Using simple fallback embeddings")
            
        except Exception as e:
            print(f"❌ Error initializing embedding model: {e}")
            self.embedding_model = None

    def store_document_metadata(self, filename: str, user_labels: dict = None):
        """Store metadata for uploaded documents with document separation"""
        try:
            # Create unique document ID
            doc_id = f"{filename}_{hash(filename)}_{int(time.time())}"
            
            metadata = {
                'filename': filename,
                'doc_id': doc_id,
                'upload_timestamp': datetime.now().isoformat(),
                'user_labels': user_labels or {},
                'processed': False,
                'chunks_created': 0
            }
            
            self.document_metadata[filename] = metadata
            self.document_registry[doc_id] = metadata
            self.document_chunks_by_file[filename] = []
            
            print(f"✅ Metadata stored for document: {filename} (ID: {doc_id})")
            
        except Exception as e:
            print(f"❌ Error storing metadata for {filename}: {str(e)}")
            raise
    def create_vector_store(self, chunks):
        """Simple vector store creation"""
        print(f"🔍 Creating vector store from {len(chunks)} chunks...")
        if not hasattr(self, 'document_chunks'):
            self.document_chunks = []
        
        for chunk in chunks:
            if isinstance(chunk, dict):
                self.document_chunks.append(chunk)
        print(document_chunks,"doccumnent shunks.....")
        print(f"✅ Vector store created with {len(self.document_chunks)} chunks")
   
    def get_document_metadata(self, filename: str = None):
        """Get metadata for a specific document or all documents"""
        if filename:
            return self.document_metadata.get(filename, {})
        return self.document_metadata

    def _advanced_search_in_chunks(self, query: str, chunks: List[Dict], top_k: int) -> List[Dict]:
        """Advanced search with better relevance scoring"""
        if not chunks:
            return []
        
        # Ensure top_k is an integer
        if isinstance(top_k, str):
            try:
                top_k = int(top_k)
            except (ValueError, TypeError):
                top_k = 5
        elif not isinstance(top_k, int):
            top_k = 5
        
        results = []
        query_lower = query.lower()
        
        # Extract key terms from query (remove common words)
        common_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'what', 'how', 'when', 'where', 'why'}
        query_terms = [word for word in query_lower.split() if word not in common_words and len(word) > 2]
        
        # Create phrase patterns for better matching
        query_phrases = self._extract_phrases(query_lower)
        
        for chunk in chunks:
            content = chunk.get('content', '')
            if not content:
                continue
                
            content_lower = content.lower()
            
            # Calculate comprehensive relevance score
            score = self._calculate_relevance_score(content_lower, query_lower, query_terms, query_phrases)
            
            if score > 0:
                result = {
                    'content': content,
                    'source': chunk.get('source'),
                    'doc_id': chunk.get('doc_id'),
                    'document_label': chunk.get('document_label', chunk.get('source', 'Unknown')),
                    'chunk_id': chunk.get('chunk_id'),
                    'score': score,
                    'relevanceScore': score,
                    'chunk_index': chunk.get('chunk_index', 0)
                }
                results.append(result)
        
        # Sort by score and return top results
        results.sort(key=lambda x: x.get('score', 0), reverse=True)
        return results[:top_k]

    def _extract_phrases(self, query: str) -> List[str]:
        """Extract meaningful phrases from query"""
        phrases = []
        
        # Look for quoted phrases
        quoted_phrases = re.findall(r'"([^"]*)"', query)
        phrases.extend(quoted_phrases)
        
        # Extract 2-3 word phrases
        words = query.split()
        for i in range(len(words) - 1):
            if len(words[i]) > 2 and len(words[i + 1]) > 2:
                phrases.append(f"{words[i]} {words[i + 1]}")
            
            if i < len(words) - 2 and len(words[i + 2]) > 2:
                phrases.append(f"{words[i]} {words[i + 1]} {words[i + 2]}")
        
        return phrases

    def _calculate_relevance_score(self, content: str, query: str, query_terms: List[str], query_phrases: List[str]) -> float:
        """Calculate comprehensive relevance score"""
        if not content:
            return 0.0
        
        score = 0.0
        content_words = content.split()
        content_len = len(content_words)
        
        if content_len == 0:
            return 0.0
        
        # 1. Exact phrase matching (highest weight)
        for phrase in query_phrases:
            if phrase in content:
                phrase_count = content.count(phrase)
                score += phrase_count * 5.0  # High weight for phrase matches
        
        # 2. Term frequency scoring
        for term in query_terms:
            if term in content:
                term_count = content.count(term)
                # TF-IDF-like scoring
                tf = term_count / content_len
                score += tf * 3.0  # Medium weight for term matches
        
        # 3. Proximity scoring (terms appearing close together)
        if len(query_terms) > 1:
            proximity_score = self._calculate_proximity_score(content, query_terms)
            score += proximity_score * 2.0
        
        # 4. Position scoring (earlier matches get higher scores)
        for term in query_terms:
            first_occurrence = content.find(term)
            if first_occurrence != -1:
                position_score = max(0, 1 - (first_occurrence / len(content)))
                score += position_score * 1.0
        
        # 5. Density bonus (multiple terms in shorter content)
        matched_terms = sum(1 for term in query_terms if term in content)
        if matched_terms > 1:
            density = matched_terms / len(query_terms)
            score += density * 1.5
        
        return score

    def _calculate_proximity_score(self, content: str, query_terms: List[str]) -> float:
        """Calculate how close query terms appear to each other"""
        positions = {}
        words = content.split()
        
        # Find positions of all query terms
        for term in query_terms:
            positions[term] = []
            for i, word in enumerate(words):
                if term in word.lower():
                    positions[term].append(i)
        
        # Calculate proximity scores
        proximity_score = 0.0
        for i, term1 in enumerate(query_terms):
            for term2 in query_terms[i + 1:]:
                if positions[term1] and positions[term2]:
                    min_distance = min(
                        abs(pos1 - pos2)
                        for pos1 in positions[term1]
                        for pos2 in positions[term2]
                    )
                    # Closer terms get higher scores
                    proximity_score += max(0, 10 - min_distance) / 10.0
        
        return proximity_score

    def document_aware_search(self, query: str, target_document: str = None, top_k: int = 5) -> List[Dict]:
        """Enhanced document-aware search with improved relevance"""
        
        # Ensure top_k is an integer
        if isinstance(top_k, str):
            try:
                top_k = int(top_k)
            except (ValueError, TypeError):
                top_k = 5
        elif not isinstance(top_k, int):
            top_k = 5
        
        print(f"🔍 Performing document-aware search for: '{query}'")
        
        if target_document:
            # Search only within specified document
            if target_document in self.document_chunks_by_file:
                doc_chunks = self.document_chunks_by_file[target_document]
                print(f"📋 Searching in specific document: {target_document} ({len(doc_chunks)} chunks)")
                return self._advanced_search_in_chunks(query, doc_chunks, top_k)
            else:
                print(f"⚠️ Document '{target_document}' not found. Searching all documents.")
        
        # Search all documents with document separation
        all_results = []
        
        for filename, chunks in self.document_chunks_by_file.items():
            if not chunks:
                continue
                
            doc_results = self._advanced_search_in_chunks(query, chunks, top_k)
            
            # Add document identifier to results
            for result in doc_results:
                result['source_document'] = filename
                metadata = self.document_metadata.get(filename, {})
                result['document_label'] = metadata.get('user_labels', {}).get('label', filename)
                
            all_results.extend(doc_results)
        
        # Sort by relevance and return top results
        all_results.sort(key=lambda x: x.get('score', 0), reverse=True)
        print(f"✅ Found {len(all_results)} total results, returning top {min(top_k, len(all_results))}")
        
        return all_results[:top_k]

    def multi_llm_search_and_answer(self, query: str, target_document: str = None, top_k: int = 5, user_preferences: Dict = None) -> Dict:
        """Comprehensive search and answer using enhanced multi-LLM architecture"""
        
        # Ensure top_k is an integer
        if isinstance(top_k, str):
            try:
                top_k = int(top_k)
            except (ValueError, TypeError):
                top_k = 5
        elif not isinstance(top_k, int):
            top_k = 5
        
        print(f"🔍 Multi-LLM processing query: '{query}' (target: {target_document}, top_k: {top_k})")
        
        # Infer target document if not specified
        if not target_document:
            target_document = self.infer_target_document_from_query(query)
            if target_document:
                print(f"📋 Inferred target document: {target_document}")
        
        # Step 1: Perform enhanced document-aware search
        search_results = self.document_aware_search(query, target_document, top_k)
        print(f"🔍 Found {len(search_results)} search results from enhanced search")
        print("searchresult................",search_results)
        if not search_results:
            available_docs = [info['filename'] for info in self.get_available_documents()]
            return {
                'status': 'no_results',
                'final_answer': f"No relevant information found for '{query}' in the processed documents.\n\nAvailable documents: {', '.join(available_docs)}\n\nTry rephrasing your question or check if the information exists in the uploaded documents.",
                'extraction_result': {'entities': [], 'confidence_score': 0.0},
                'rag_result': {'response': '', 'confidence_level': 0.0},
                'search_results': [],
                'target_document': target_document
            }
        
        # Initialize safe defaults
        extraction_result = {'entities': [], 'confidence_score': 0.0}
        rag_result = {'response': '', 'confidence_level': 0.0}
        
        # Step 2: Extract entities from search results
        print("📊 Extracting entities...")
        try:
            extraction_result = self.extraction_llm.extract_entities(search_results, query)
            print("extraction_result--------------",extraction_result)
            if extraction_result:
                print(f"✅ Entities extracted with confidence: {extraction_result.get('confidence_score', 0)}")
            else:
                extraction_result = {'entities': [], 'confidence_score': 0.0}
        except Exception as e:
            print(f"⚠️ Entity extraction failed: {str(e)}")
            extraction_result = {'entities': [], 'confidence_score': 0, 'error': str(e)}
        
        # Step 3: Generate RAG-based response
        print("🧠 Generating RAG response...")
        try:
            rag_results = self.rag_llm.generate_rag_response(query, search_results, extraction_result)
            # if rag_results:
            #     print(f"✅ RAG response generated with confidence: {rag_result.get('confidence_level', 0)}")
            # else:
            #     rag_result = {'response': '', 'confidence_level': 0.0}
        except Exception as e:
            print(f"⚠️ RAG response generation failed: {str(e)}")
            rag_results = {'response': 'Error generating RAG response', 'confidence_level': 0, 'error': str(e)}
        
        # Step 4: Generate final answer with enhanced fallback
        print("✨ Generating final answer...")
        try:
            print(extraction_result,"extraction_result==================")
            print(rag_results,"rag_result===================")
            final_answer = self.final_answer_llm.generate_final_answer(
                query, extraction_result, rag_results, user_preferences, target_document
            )
            print(final_answer,"final_answer=============")
            if not final_answer or len(final_answer.strip()) < 10:
                raise Exception("Generated answer too short or empty")
                
        except Exception as e:
            print(f"⚠️ Final answer generation failed: {str(e)}")
            # Enhanced intelligent fallback
            final_answer = self._create_intelligent_fallback_answer(query, search_results, target_document)
        
        return {
            'status': 'success',
            'final_answer': final_answer,
            'extraction_result': extraction_result,
            'rag_result': rag_result,
            'search_results': search_results,
            'target_document': target_document,
            'metadata': {
                'extraction_confidence': extraction_result.get('confidence_score', 0),
                'rag_confidence': rag_result.get('confidence_level', 0),
                'sources_count': len(search_results),
                'multi_llm_processing': True,
                'document_aware': True,
                'search_method': 'enhanced_relevance'
            }
        }

    def _create_intelligent_fallback_answer(self, query: str, search_results: List[Dict], target_document: str = None) -> str:
        """Create an intelligent structured answer when LLMs fail"""
        if not search_results:
            return f"I couldn't find relevant information for '{query}' in the available documents."
        
        # Group results by document
        results_by_doc = {}
        for result in search_results:
            doc_name = result.get('document_label', result.get('source', 'Unknown'))
            if doc_name not in results_by_doc:
                results_by_doc[doc_name] = []
            results_by_doc[doc_name].append(result)
        
        # Create structured answer
        answer_parts = []
        
        # Add context about the query
        document_context = f" in {target_document}" if target_document else " across your documents"
        answer_parts.append(f"Based on my search for '{query}'{document_context}, here are the most relevant findings:")
        answer_parts.append("")
        
        # Process results by document
        for doc_name, doc_results in results_by_doc.items():
            answer_parts.append(f"**From {doc_name}:**")
            
            for i, result in enumerate(doc_results[:3], 1):  # Limit to top 3 per document
                content = result.get('content', '')
                if content:
                    # Extract the most relevant sentence/paragraph
                    relevant_text = self._extract_relevant_portion(content, query)
                    if relevant_text:
                        score = result.get('score', 0)
                        answer_parts.append(f"{i}. {relevant_text} (Relevance: {score:.2f})")
                    else:
                        # Fallback to truncated content
                        truncated = content[:300].strip()
                        if len(content) > 300:
                            truncated += "..."
                        answer_parts.append(f"{i}. {truncated}")
            
            answer_parts.append("")  # Add spacing between documents
        
        # Add helpful footer
        total_docs = len(results_by_doc)
        total_results = len(search_results)
        answer_parts.append(f"*Found {total_results} relevant passages across {total_docs} document{'s' if total_docs != 1 else ''}. Relevance scores indicate how closely each passage matches your query.*")
        
        return "\n".join(answer_parts)

    def _extract_relevant_portion(self, content: str, query: str) -> str:
        """Extract the most relevant portion of content based on query"""
        if not content or not query:
            return ""
        
        # Split into sentences
        sentences = re.split(r'[.!?]+', content)
        if not sentences:
            return content[:200] + "..." if len(content) > 200 else content
        
        query_lower = query.lower()
        query_terms = [word for word in query_lower.split() if len(word) > 2]
        
        # Score each sentence
        sentence_scores = []
        for sentence in sentences:
            if len(sentence.strip()) < 20:  # Skip very short sentences
                continue
                
            sentence_lower = sentence.lower()
            score = sum(1 for term in query_terms if term in sentence_lower)
            
            # Bonus for exact phrases
            if len(query_terms) > 1:
                for i in range(len(query_terms) - 1):
                    phrase = f"{query_terms[i]} {query_terms[i + 1]}"
                    if phrase in sentence_lower:
                        score += 2
            
            sentence_scores.append((sentence.strip(), score))
        
        # Return the highest scoring sentence(s)
        if sentence_scores:
            sentence_scores.sort(key=lambda x: x[1], reverse=True)
            best_sentence = sentence_scores[0][0]
            
            # If the best sentence is too short, try to include context
            if len(best_sentence) < 100 and len(sentence_scores) > 1:
                second_best = sentence_scores[1][0]
                combined = f"{best_sentence}. {second_best}"
                if len(combined) < 300:
                    return combined
            
            return best_sentence
        
        # Fallback to first part of content
        return content[:200] + "..." if len(content) > 200 else content
    def infer_target_document_from_query(self, query: str) -> str:
        
        print(f"🔍 Inferring target document from query: '{query}'")
        query_lower = query.lower()
        # Enhanced manufacturer detection patterns
        manufacturer_patterns = {
            'ford': ['ford', 'f-150', 'f-250', 'f-350', 'mustang', 'explorer', 'escape'],
            'chrysler': ['chrysler', 'dodge', 'ram', 'jeep', 'fiat', 'plymouth', 'mopar'],
            'toyota': ['toyota', 'camry', 'corolla', 'prius', 'rav4', 'highlander'],
            'honda': ['honda', 'civic', 'accord', 'crv', 'pilot', 'odyssey'],
            'gm': ['gm', 'general motors', 'chevrolet', 'chevy', 'cadillac', 'buick', 'gmc'],
            'bmw': ['bmw', 'mini cooper', 'rolls royce'],
            'mercedes': ['mercedes', 'benz', 'mercedes-benz', 'smart car']
        }
        
        # Enhanced state/jurisdiction detection
        state_patterns = {
            'california': ['california', 'ca ', ' ca', 'calif', 'golden state'],
            'florida': ['florida', 'fl ', ' fl', 'sunshine state'],
            'texas': ['texas', 'tx ', ' tx', 'lone star'],
            'new york': ['new york', 'ny ', ' ny', 'empire state'],
        }
        
        # Detect manufacturer from query
        detected_manufacturer = None
        for manufacturer, patterns in manufacturer_patterns.items():
            if any(pattern in query_lower for pattern in patterns):
                detected_manufacturer = manufacturer
                print(f"📋 Detected manufacturer: {detected_manufacturer}")
                break
        
        # Detect state from query
        detected_state = None
        for state, patterns in state_patterns.items():
            if any(pattern in query_lower for pattern in patterns):
                detected_state = state
                print(f"📋 Detected state: {detected_state}")
                break
        
        # Score all documents for best match
        document_scores = {}
        
        for filename, metadata in self.document_metadata.items():
            score = 0
            filename_lower = filename.lower()
            
            print(f"📄 Evaluating document: {filename}")
            
            # 1. Check filename for manufacturer patterns
            if detected_manufacturer:
                manufacturer_patterns_list = manufacturer_patterns[detected_manufacturer]
                for pattern in manufacturer_patterns_list:
                    if pattern in filename_lower:
                        score += 5
                        print(f"   ✅ Manufacturer match in filename: +5 (pattern: {pattern})")
            
            # 2. Check filename for state patterns
            if detected_state:
                state_patterns_list = state_patterns[detected_state]
                for pattern in state_patterns_list:
                    if pattern in filename_lower:
                        score += 4
                        print(f"   ✅ State match in filename: +4 (pattern: {pattern})")
            
            # 3. Check user labels for manufacturer/state
            user_labels = metadata.get('user_labels', {})
            for label_key, label_value in user_labels.items():
                if isinstance(label_value, str):
                    label_lower = label_value.lower()
                    
                    # Check for manufacturer in labels
                    if detected_manufacturer:
                        manufacturer_patterns_list = manufacturer_patterns[detected_manufacturer]
                        for pattern in manufacturer_patterns_list:
                            if pattern in label_lower:
                                score += 4
                                print(f"   ✅ Manufacturer match in label '{label_key}': +4 (pattern: {pattern})")
                    
                    # Check for state in labels
                    if detected_state:
                        state_patterns_list = state_patterns[detected_state]
                        for pattern in state_patterns_list:
                            if pattern in label_lower:
                                score += 3
                                print(f"   ✅ State match in label '{label_key}': +3 (pattern: {pattern})")
            
            # 4. For XLSX files, check document content if available
            if filename.lower().endswith('.xlsx') and hasattr(self, 'document_chunks_by_file'):
                doc_chunks = self.document_chunks_by_file.get(filename, [])
                if doc_chunks:
                    # Sample first few chunks to check content
                    sample_content = ""
                    for chunk in doc_chunks[:3]:  # Check first 3 chunks
                        sample_content += chunk.get('content', '').lower() + " "
                    
                    if sample_content:
                        # Check for manufacturer in content
                        if detected_manufacturer:
                            manufacturer_patterns_list = manufacturer_patterns[detected_manufacturer]
                            for pattern in manufacturer_patterns_list:
                                if pattern in sample_content:
                                    score += 3
                                    print(f"   ✅ Manufacturer match in XLSX content: +3 (pattern: {pattern})")
                                    break  # Only add points once per manufacturer
                        
                        # Check for state in content
                        if detected_state:
                            state_patterns_list = state_patterns[detected_state]
                            for pattern in state_patterns_list:
                                if pattern in sample_content:
                                    score += 2
                                    print(f"   ✅ State match in XLSX content: +2 (pattern: {pattern})")
                                    break  # Only add points once per state
            
            # 5. Check for legal terms in query
            legal_patterns = {
                'warranty': ['warranty', 'warr', 'guarantee'],
                'compensation': ['compensation', 'comp', 'payment', 'reimburse'],
                'statute': ['statute', 'law', 'regulation', 'code'],
                'repair': ['repair', 'fix', 'service', 'maintenance']
            }
            
            for category, terms in legal_patterns.items():
                if any(term in query_lower for term in terms):
                    if category in filename_lower or any(term in filename_lower for term in terms):
                        score += 1
                        print(f"   ✅ Legal term match: +1 (category: {category})")
            
            # 6. Direct word matches between query and filename
            filename_words = set(re.findall(r'\b\w{3,}\b', filename_lower))
            query_words = set(re.findall(r'\b\w{3,}\b', query_lower))
            common_words = filename_words & query_words
            if common_words:
                word_score = len(common_words)
                score += word_score
                print(f"   ✅ Common words: +{word_score} (words: {common_words})")
            
            document_scores[filename] = score
            print(f"   📊 Final score for {filename}: {score}")
        
        # Find the best matching document
        if document_scores:
            best_match = max(document_scores.items(), key=lambda x: x[1])
            best_filename, best_score = best_match
            
            print(f"📊 Document scores: {document_scores}")
            
            # Only return a match if the score is meaningful (> 0)
            if best_score > 0:
                print(f"✅ Selected target document: {best_filename} (score: {best_score})")
                return best_filename
            else:
                print("⚠️ No meaningful matches found")
                return None
        else:
            print("⚠️ No documents available for scoring")
            return None
    # def infer_target_document_from_query(self, query: str) -> str:
    #     """Enhanced document inference from query"""
    #     query_lower = query.lower()
        
    #     # Enhanced state/jurisdiction detection
    #     state_patterns = {
    #         'california': ['california', 'ca ', ' ca', 'calif', 'golden state'],
    #         'florida': ['florida', 'fl ', ' fl', 'sunshine state'],
    #         'texas': ['texas', 'tx ', ' tx', 'lone star'],
    #         'new york': ['new york', 'ny ', ' ny', 'empire state'],
    #         # Add more states as needed
    #     }
        
    #     for state, patterns in state_patterns.items():
    #         if any(pattern in query_lower for pattern in patterns):
    #             for filename, metadata in self.document_metadata.items():
    #                 filename_lower = filename.lower()
    #                 if any(pattern in filename_lower for pattern in patterns):
    #                     return filename
        
    #     # Check for specific legal terms that might indicate document type
    #     legal_patterns = {
    #         'warranty': ['warranty', 'warr', 'guarantee'],
    #         'compensation': ['compensation', 'comp', 'payment', 'reimburse'],
    #         'statute': ['statute', 'law', 'regulation', 'code'],
    #         'repair': ['repair', 'fix', 'service', 'maintenance']
    #     }
        
    #     query_legal_terms = []
    #     for category, terms in legal_patterns.items():
    #         if any(term in query_lower for term in terms):
    #             query_legal_terms.append(category)
        
    #     # Match against document names/labels
    #     best_match = None
    #     best_score = 0
        
    #     for filename, metadata in self.document_metadata.items():
    #         score = 0
    #         filename_lower = filename.lower()
            
    #         # Score based on legal terms
    #         for term in query_legal_terms:
    #             if term in filename_lower:
    #                 score += 2
            
    #         # Score based on user labels
    #         user_labels = metadata.get('user_labels', {})
    #         for label_value in user_labels.values():
    #             if isinstance(label_value, str):
    #                 label_lower = label_value.lower()
    #                 if any(word in label_lower for word in query_lower.split()):
    #                     score += 1
            
    #         # Score based on direct filename matches
    #         filename_words = re.findall(r'\b\w+\b', filename_lower)
    #         query_words = re.findall(r'\b\w+\b', query_lower)
    #         common_words = set(filename_words) & set(query_words)
    #         score += len(common_words)
            
    #         if score > best_score:
    #             best_score = score
    #             best_match = filename
        
    #     return best_match if best_score > 0 else None

    def _create_enhanced_chunks(self, text: str, filename: str, doc_metadata: Dict) -> List[Dict]:
        """Create chunks with enhanced metadata and better content preservation"""
        try:
            if hasattr(self, 'create_chunks'):
                base_chunks = self.create_chunks(text)
            else:
                base_chunks = self._smart_chunk_text(text)
            
            enhanced_chunks = []
            doc_id = doc_metadata.get('doc_id', f"{filename}_{hash(filename)}_{int(time.time())}")
            
            for i, chunk in enumerate(base_chunks):
                chunk_content = chunk if isinstance(chunk, str) else chunk.get('content', '')
                
                # Clean and preserve chunk content
                chunk_content = self._clean_chunk_content(chunk_content)
                
                enhanced_chunk = {
                    'content': chunk_content,
                    'source': filename,
                    'doc_id': doc_id,
                    'chunk_id': f"{doc_id}_{i}",
                    'document_metadata': doc_metadata,
                    'chunk_index': i,
                    'total_chunks': len(base_chunks),
                    'document_label': doc_metadata.get('user_labels', {}).get('label', filename),
                    'word_count': len(chunk_content.split()),
                    'char_count': len(chunk_content)
                }
                
                if isinstance(chunk, dict):
                    enhanced_chunk.update(chunk)
                
                enhanced_chunks.append(enhanced_chunk)
            
            # Store chunks by document
            self.document_chunks_by_file[filename] = enhanced_chunks
            
            print(f"✅ Created {len(enhanced_chunks)} enhanced chunks for {filename}")
            return enhanced_chunks
            
        except Exception as e:
            print(f"❌ Error creating enhanced chunks: {e}")
            return []

    def _clean_chunk_content(self, content: str) -> str:
        """Clean and normalize chunk content"""
        if not content:
            return ""
        
        # Remove excessive whitespace
        content = re.sub(r'\s+', ' ', content.strip())
        
        # Remove incomplete sentences at the beginning and end
        sentences = re.split(r'[.!?]+', content)
        if len(sentences) > 2:
            # Keep complete sentences
            complete_sentences = [s.strip() for s in sentences[1:-1] if len(s.strip()) > 10]
            if complete_sentences:
                content = '. '.join(complete_sentences) + '.'
        
        return content

    def _smart_chunk_text(self, text: str, target_chunk_size: int = 800, overlap: int = 100) -> List[str]:
        """Smart text chunking that preserves semantic boundaries"""
        if not text:
            return []
        
        chunks = []
        
        # First, try to split by paragraphs
        paragraphs = [p.strip() for p in text.split('\n\n') if p.strip()]
        
        current_chunk = ""
        
        for paragraph in paragraphs:
            # If adding this paragraph would exceed target size
            if len(current_chunk) + len(paragraph) > target_chunk_size and current_chunk:
                # Try to split at sentence boundaries within the chunk
                chunk_sentences = self._split_at_sentence_boundaries(current_chunk, target_chunk_size)
                chunks.extend(chunk_sentences)
                current_chunk = paragraph
            else:
                current_chunk += ("\n\n" + paragraph) if current_chunk else paragraph
        
        # Handle remaining content
        if current_chunk:
            if len(current_chunk) > target_chunk_size:
                chunk_sentences = self._split_at_sentence_boundaries(current_chunk, target_chunk_size)
                chunks.extend(chunk_sentences)
            else:
                chunks.append(current_chunk)
        
        # Add overlap between chunks
        overlapped_chunks = self._add_overlap(chunks, overlap)
        
        return overlapped_chunks

    def _split_at_sentence_boundaries(self, text: str, max_size: int) -> List[str]:
        """Split text at sentence boundaries"""
        sentences = re.split(r'(?<=[.!?])\s+', text)
        chunks = []
        current_chunk = ""
        
        for sentence in sentences:
            if len(current_chunk) + len(sentence) > max_size and current_chunk:
                chunks.append(current_chunk.strip())
                current_chunk = sentence
            else:
                current_chunk += (" " + sentence) if current_chunk else sentence
        
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        return chunks

    def _add_overlap(self, chunks: List[str], overlap_chars: int) -> List[str]:
        """Add overlap between consecutive chunks"""
        if len(chunks) <= 1:
            return chunks
        
        overlapped_chunks = [chunks[0]]
        
        for i in range(1, len(chunks)):
            prev_chunk = chunks[i - 1]
            current_chunk = chunks[i]
            
            # Get overlap from end of previous chunk
            overlap_text = prev_chunk[-overlap_chars:] if len(prev_chunk) > overlap_chars else prev_chunk
            
            # Combine with current chunk
            overlapped_chunk = overlap_text + " " + current_chunk
            overlapped_chunks.append(overlapped_chunk)
        
        return overlapped_chunks

    def get_available_documents(self) -> List[Dict]:
        """Get list of all processed documents with enhanced info"""
        return [
            {
                'filename': filename,
                'label': metadata.get('user_labels', {}).get('label', filename),
                'doc_id': metadata.get('doc_id'),
                'chunks_count': len(self.document_chunks_by_file.get(filename, [])),
                'processed_at': metadata.get('upload_timestamp'),
                'user_labels': metadata.get('user_labels', {}),
                'processed': metadata.get('processed', False),
                'total_words': sum(chunk.get('word_count', 0) for chunk in self.document_chunks_by_file.get(filename, []))
            }
            for filename, metadata in self.document_metadata.items()
        ]

    def initialize_llm_clients(self, groq_api_key: str = None, openai_api_key: str = None):
        """Initialize or reinitialize LLM clients - prioritizing Groq"""
        
        if groq_api_key:
            try:
                self.extraction_llm.initialize_groq(groq_api_key)
                self.rag_llm.initialize_groq(groq_api_key)
                self.final_answer_llm.initialize_groq(groq_api_key)
                print("✅ Groq clients initialized for all LLMs")
                return True
            except Exception as e:
                print(f"❌ Error initializing Groq clients: {str(e)}")
        
        if openai_api_key:
            try:
                self.extraction_llm.initialize_openai(openai_api_key)
                self.rag_llm.initialize_openai(openai_api_key)
                self.final_answer_llm.initialize_openai(openai_api_key)
                print("✅ OpenAI clients initialized for all LLMs")
                return True
            except Exception as e:
                print(f"❌ Error initializing OpenAI clients: {str(e)}")
        
        print("⚠️ No API keys provided or initialization failed")
        return False

    def get_health_status(self):
        """Get health status of the processor"""
        return {
            'processor_initialized': True,
            'document_metadata_count': len(self.document_metadata),
            'extraction_llm_ready': hasattr(self.extraction_llm, 'client') and self.extraction_llm.client is not None,
            'rag_llm_ready': hasattr(self.rag_llm, 'client') and self.rag_llm.client is not None,
            'final_answer_llm_ready': hasattr(self.final_answer_llm, 'client') and self.final_answer_llm.client is not None,
            'total_documents': len(self.document_metadata),
            'total_chunks': sum(len(chunks) for chunks in self.document_chunks_by_file.values()),
            'embedding_model_type': type(self.embedding_model).__name__ if self.embedding_model else 'None'
        }

    def process_document_with_metadata(self, file_content, filename: str, user_labels: dict = None):
        """Process document and store metadata"""
        try:
            # Store metadata first
            self.store_document_metadata(filename, user_labels)
            
            # Process the document using parent class method
            result = self.process_document(file_content, filename)
            
            # Update metadata after processing
            if filename in self.document_metadata:
                self.document_metadata[filename]['processed'] = True
                self.document_metadata[filename]['chunks_created'] = len(result) if result else 0
            
            return result
            
        except Exception as e:
            print(f"❌ Error processing document {filename}: {str(e)}")
            raise

    def process_legal_document_with_metadata(self, file_path: str, metadata: Dict = None) -> List[Dict]:
        """Process document with metadata integration"""
        filename = os.path.basename(file_path)
        
        # Store metadata first
        if metadata:
            self.store_document_metadata(filename, metadata)
        
        doc_metadata = self.get_document_metadata(filename)
        
        try:
            # Extract text based on file type
            if file_path.endswith('.pdf'):
                text = self._extract_pdf_text(file_path)
            elif file_path.endswith('.docx'):
                text = self._extract_docx_text(file_path)
            elif file_path.endswith('.txt'):
                text = self._extract_txt_text(file_path)
            elif file_path.endswith('.xlsx'):
                text = self._extract_xlsx_text(file_path)
            else:
                raise ValueError(f"Unsupported file type: {file_path}")
            
            if not text or len(text.strip()) < 50:
                print(f"⚠️ Document {filename} appears to be empty or too short")
                return []
            
            # Create chunks with enhanced metadata
            chunks = self._create_enhanced_chunks(text, filename, doc_metadata)
            print(chunks,"chunks.........")
            # Mark as processed
            if filename in self.document_metadata:
                self.document_metadata[filename]['processed'] = True
                self.document_metadata[filename]['chunks_created'] = len(chunks)
            
            print(f"✅ Processed {filename}: {len(chunks)} chunks with metadata")
            return chunks
            
        except Exception as e:
            print(f"❌ Error processing {filename}: {e}")
            return []

    def _extract_pdf_text(self, file_path: str) -> str:
        """Extract text from PDF file"""
        try:
            import PyPDF2
            text = ""
            with open(file_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                for page in reader.pages:
                    text += page.extract_text() + "\n"
            return text.strip()
        except Exception as e:
            print(f"❌ Error extracting PDF text: {e}")
            return ""

    def _extract_docx_text(self, file_path: str) -> str:
        """Extract text from DOCX file"""
        try:
            from docx import Document
            doc = Document(file_path)
            text = ""
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            return text.strip()
        except Exception as e:
            print(f"❌ Error extracting DOCX text: {e}")
            return ""

    def _extract_txt_text(self, file_path: str) -> str:
        """Extract text from TXT file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                return file.read().strip()
        except Exception as e:
            print(f"❌ Error extracting TXT text: {e}")
            return ""

    def _extract_xlsx_text(self, file_path: str) -> str:
        """Extract text from XLSX file"""
        try:
            import pandas as pd
            df = pd.read_excel(file_path)
            text = df.to_string(index=False)
            return text.strip()
        except Exception as e:
            print(f"❌ Error extracting XLSX text: {e}")
            return ""

    def search_specific_definition(self, term: str, target_document: str = None) -> Dict:
        """Specialized search for legal definitions"""
        
        # Create definition-specific query patterns
        definition_queries = [
            f'definition of "{term}"',
            f'"{term}" means',
            f'"{term}" is defined as',
            f'"{term}" shall mean',
            f'for purposes of this section "{term}"'
        ]
        
        all_results = []
        
        for query in definition_queries:
            results = self.document_aware_search(query, target_document, top_k=3)
            for result in results:
                result['query_pattern'] = query
                result['definition_specific'] = True
            all_results.extend(results)
        
        # Remove duplicates and sort by relevance
        seen_chunks = set()
        unique_results = []
        
        for result in all_results:
            chunk_id = result.get('chunk_id', '')
            if chunk_id not in seen_chunks:
                seen_chunks.add(chunk_id)
                unique_results.append(result)
        
        unique_results.sort(key=lambda x: x.get('score', 0), reverse=True)
        
        return {
            'term': term,
            'target_document': target_document,
            'definition_results': unique_results[:5],
            'search_patterns_used': definition_queries,
            'total_matches': len(unique_results)
        }

    def get_document_summary(self, filename: str) -> Dict:
        """Get comprehensive summary of a specific document"""
        if filename not in self.document_metadata:
            return {'error': f'Document {filename} not found'}
        
        metadata = self.document_metadata[filename]
        chunks = self.document_chunks_by_file.get(filename, [])
        
        # Basic statistics
        total_words = sum(chunk.get('word_count', 0) for chunk in chunks)
        total_chars = sum(chunk.get('char_count', 0) for chunk in chunks)
        
        # Extract key terms (simple frequency analysis)
        all_text = ' '.join(chunk.get('content', '') for chunk in chunks).lower()
        words = re.findall(r'\b\w{4,}\b', all_text)  # Words with 4+ characters
        
        # Count word frequencies (excluding common words)
        common_words = {'that', 'this', 'with', 'from', 'they', 'been', 'have', 'their', 'said', 'each', 'which', 'shall', 'will', 'may', 'must', 'such', 'other', 'any', 'all', 'section', 'part', 'chapter'}
        word_freq = {}
        for word in words:
            if word not in common_words:
                word_freq[word] = word_freq.get(word, 0) + 1
        
        # Get top terms
        top_terms = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:20]
        
        return {
            'filename': filename,
            'metadata': metadata,
            'statistics': {
                'total_chunks': len(chunks),
                'total_words': total_words,
                'total_characters': total_chars,
                'average_chunk_size': total_words // len(chunks) if chunks else 0
            },
            'top_terms': top_terms,
            'document_label': metadata.get('user_labels', {}).get('label', filename),
            'processed': metadata.get('processed', False),
            'created_at': metadata.get('upload_timestamp')
        }

    def bulk_search_across_documents(self, queries: List[str], top_k_per_query: int = 3) -> Dict:
        """Perform multiple searches across all documents efficiently"""
        results = {}
        
        for query in queries:
            print(f"🔍 Processing query: {query}")
            search_results = self.document_aware_search(query, target_document=None, top_k=top_k_per_query)
            
            results[query] = {
                'results': search_results,
                'result_count': len(search_results),
                'documents_found': list(set(r.get('source_document', r.get('source', '')) for r in search_results))
            }
        
        return {
            'queries_processed': len(queries),
            'total_results': sum(len(r['results']) for r in results.values()),
            'query_results': results,
            'available_documents': [doc['filename'] for doc in self.get_available_documents()]
        }
    def delete_document_from_processor(self, document_id: str, filename: str) -> bool:
        """Delete specific document from processor storage"""
        try:
            deleted_something = False
            
            # Remove from document_metadata
            if hasattr(self, 'document_metadata') and filename in self.document_metadata:
                del self.document_metadata[filename]
                deleted_something = True
                print(f"✅ Removed {filename} from document metadata")
            
            # Remove from document_chunks_by_file
            if hasattr(self, 'document_chunks_by_file'):
                keys_to_remove = []
                for key in self.document_chunks_by_file.keys():
                    if key == filename or key == document_id:
                        keys_to_remove.append(key)
                
                for key in keys_to_remove:
                    del self.document_chunks_by_file[key]
                    deleted_something = True
                    print(f"✅ Removed {key} from document chunks by file")
            
            return deleted_something
            
        except Exception as e:
            print(f"❌ Error deleting document from processor: {e}")
            return False
    
    def get_document_status(self, filename: str) -> Dict:
        """Get comprehensive status of a document in the processor"""
        try:
            status = {
                'filename': filename,
                'exists_in_metadata': False,
                'is_processed': False,
                'chunks_count': 0,
                'in_vector_store': False,
                'document_id': None,
                'metadata': {}
            }
            
            # Check metadata
            if hasattr(self, 'document_metadata') and filename in self.document_metadata:
                metadata = self.document_metadata[filename]
                status.update({
                    'exists_in_metadata': True,
                    'is_processed': metadata.get('processed', False),
                    'chunks_count': metadata.get('chunks_created', 0),
                    'document_id': metadata.get('document_id', filename),
                    'metadata': metadata
                })
            
            # Check chunks
            if hasattr(self, 'document_chunks_by_file') and filename in self.document_chunks_by_file:
                chunks = self.document_chunks_by_file[filename]
                status['chunks_count'] = len(chunks) if chunks else 0
            
            # Check vector store (basic check)
            if hasattr(self, 'vector_store') and self.vector_store is not None:
                status['in_vector_store'] = True
            
            return status
            
        except Exception as e:
            print(f"❌ Error getting document status: {e}")
            return {'error': str(e)}
    
    def rebuild_vector_store_without_document(self, document_id: str, filename: str):
        """Rebuild vector store excluding specific document"""
        try:
            if not hasattr(self, 'document_chunks_by_file'):
                print("⚠️ No document_chunks_by_file found")
                return False
            
            # Collect all chunks except from the target document
            remaining_chunks = []
            for file_key, chunks in self.document_chunks_by_file.items():
                if file_key != filename and file_key != document_id:
                    if isinstance(chunks, list):
                        remaining_chunks.extend(chunks)
            
            # Rebuild vector store
            if remaining_chunks:
                self.create_vector_store(remaining_chunks)
                print(f"✅ Vector store rebuilt with {len(remaining_chunks)} chunks")
                return True
            else:
                # No chunks left, clear vector store
                self.vector_store = None
                print("✅ Vector store cleared (no remaining chunks)")
                return True
                
        except Exception as e:
            print(f"❌ Error rebuilding vector store: {e}")
            return False


@app.route('/api/multi-llm-settings', methods=['POST'])
def update_multi_llm_settings():
    """Update multi-LLM API settings"""
    try:
        data = request.get_json()
        groq_api_key = data.get('groq_api_key')
        openai_api_key = data.get('openai_api_key')
        
        if not groq_api_key and not openai_api_key:
            return jsonify({
                'status': 'error',
                'message': 'At least one API key is required'
            }), 400
        
        if not processor:
            return jsonify({
                'status': 'error',
                'message': 'Processor not initialized'
            }), 500
        
        # Initialize LLM clients
        processor.initialize_llm_clients(groq_api_key, openai_api_key)
        
        # Verify initialization success
        extraction_ready = (processor.extraction_llm.groq_client is not None or 
                           processor.extraction_llm.openai_client is not None)
        rag_ready = (processor.rag_llm.groq_client is not None or 
                    processor.rag_llm.openai_client is not None)
        final_answer_ready = (processor.final_answer_llm.groq_client is not None or 
                             processor.final_answer_llm.openai_client is not None)
        
        if not (extraction_ready and rag_ready and final_answer_ready):
            return jsonify({
                'status': 'warning',
                'message': 'Some LLM clients failed to initialize',
                'llm_status': {
                    'extraction_llm': extraction_ready,
                    'rag_llm': rag_ready,
                    'final_answer_llm': final_answer_ready
                }
            }), 206
        
        return jsonify({
            'status': 'success',
            'message': 'Multi-LLM clients initialized successfully',
            'llm_status': {
                'extraction_llm': extraction_ready,
                'rag_llm': rag_ready,
                'final_answer_llm': final_answer_ready,
                'groq_enabled': groq_api_key is not None,
                'openai_enabled': openai_api_key is not None
            }
        })
            
    except Exception as e:
        print(f"Error updating multi-LLM settings: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Failed to update multi-LLM settings: {str(e)}'
        }), 500

# Add endpoint to get current multi-LLM status
@app.route('/api/multi-llm-status', methods=['GET'])
def get_multi_llm_status():
    """Get current multi-LLM configuration status"""
    try:
        if not processor:
            return jsonify({
                'status': 'error',
                'message': 'Processor not initialized'
            }), 500
        
        # Check status of each LLM component
        extraction_status = {
            'groq_ready': processor.extraction_llm.groq_client is not None,
            'openai_ready': processor.extraction_llm.openai_client is not None,
            'operational': (processor.extraction_llm.groq_client is not None or 
                           processor.extraction_llm.openai_client is not None)
        }
        
        rag_status = {
            'groq_ready': processor.rag_llm.groq_client is not None,
            'openai_ready': processor.rag_llm.openai_client is not None,
            'operational': (processor.rag_llm.groq_client is not None or 
                           processor.rag_llm.openai_client is not None)
        }
        
        final_answer_status = {
            'groq_ready': processor.final_answer_llm.groq_client is not None,
            'openai_ready': processor.final_answer_llm.openai_client is not None,
            'operational': (processor.final_answer_llm.groq_client is not None or 
                           processor.final_answer_llm.openai_client is not None)
        }
        
        overall_operational = (extraction_status['operational'] and 
                              rag_status['operational'] and 
                              final_answer_status['operational'])
        
        return jsonify({
            'status': 'success',
            'overall_operational': overall_operational,
            'llm_components': {
                'extraction_llm': extraction_status,
                'rag_llm': rag_status,
                'final_answer_llm': final_answer_status
            },
            'capabilities': {
                'entity_extraction': extraction_status['operational'],
                'cross_referenced_analysis': rag_status['operational'],
                'final_answer_generation': final_answer_status['operational'],
                'full_pipeline': overall_operational
            }
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

# Add endpoint for testing multi-LLM pipeline
@app.route('/api/test-multi-llm', methods=['POST'])
def test_multi_llm_pipeline():
    """Test the multi-LLM pipeline with sample data"""
    try:
        data = request.get_json()
        test_query = data.get('query', 'What is the warranty notice requirement?')
        
        if not processor or not document_chunks:
            return jsonify({
                'status': 'error',
                'message': 'No documents processed for testing'
            }), 400
        
        # Test multi-LLM pipeline
        result = processor.multi_llm_search_and_answer(test_query, top_k=3, user_preferences={'style': 'detailed'})
        
        return jsonify({
            'status': 'success',
            'message': 'Multi-LLM pipeline test completed',
            'test_query': test_query,
            'pipeline_result': {
                'extraction_entities': result.get('extraction_result', {}).get('state', 'N/A'),
                'rag_confidence': result.get('rag_result', {}).get('confidence_level', 0),
                'final_answer_preview': result.get('final_answer', '')[:200] + '...',
                'sources_found': len(result.get('search_results', [])),
                'processing_status': result.get('status')
            }
        })
            
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'Multi-LLM test failed: {str(e)}'
        }), 500

# Add endpoint for extraction-only testing
@app.route('/api/test-extraction', methods=['POST'])
def test_extraction_llm():
    """Test the extraction LLM independently"""
    try:
        data = request.get_json()
        text = data.get('text', '')
        query = data.get('query', 'What information can you extract?')
        
        if not text:
            return jsonify({
                'status': 'error',
                'message': 'Text is required for extraction testing'
            }), 400
        
        if not processor:
            return jsonify({
                'status': 'error',
                'message': 'Processor not initialized'
            }), 500
        
        # Test extraction
        extraction_result = processor.extraction_llm.extract_entities(text, query)
        
        return jsonify({
            'status': 'success',
            'message': 'Extraction LLM test completed',
            'extraction_result': extraction_result
        })
            
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'Extraction test failed: {str(e)}'
        }), 500

# Update the health check endpoint to include multi-LLM status
@app.route('/api/health', methods=['GET'])
# @app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    try:
        if processor:
            health_status = processor.get_health_status()
            return jsonify({
                'status': 'healthy',
                'processor_ready': True,
                **health_status
            })
        else:
            return jsonify({
                'status': 'unhealthy',
                'processor_ready': False,
                'message': 'Processor not initialized'
            }), 503
            
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

# Add endpoint for advanced multi-LLM search
@app.route('/api/search/multi-llm', methods=['POST'])
def multi_llm_search():
    """Advanced search using multi-LLM architecture with custom preferences"""
    try:
        data = request.get_json()
        query = data.get('query', '').strip()
        top_k = data.get('top_k', 5)
        
        # Advanced user preferences
        user_preferences = {
            'style': data.get('answer_style', 'detailed'),  # detailed, concise, bullet_points
            'include_sources': data.get('include_sources', True),
            'confidence_threshold': data.get('confidence_threshold', 0.7),
            'focus_areas': data.get('focus_areas', []),  # e.g., ['warranty', 'timeline', 'procedures']
            'jurisdiction_filter': data.get('jurisdiction_filter', None),  # e.g., 'Florida'
            'manufacturer_filter': data.get('manufacturer_filter', None)  # e.g., 'Ford'
        }
        
        if not query:
            return jsonify({
                'status': 'error',
                'message': 'Query is required'
            }), 400
        
        if not processor or not document_chunks:
            return jsonify({
                'status': 'error',
                'message': 'No documents processed'
            }), 400
        
        # Execute multi-LLM pipeline
        result = processor.multi_llm_search_and_answer(query, top_k, user_preferences)
        
        return jsonify({
            'status': 'success',
            'query': query,
            'final_answer': result.get('final_answer'),
            'detailed_analysis': {
                'extraction_result': result.get('extraction_result', {}),
                'rag_analysis': result.get('rag_result', {}),
                'confidence_scores': {
                    'extraction': result.get('metadata', {}).get('extraction_confidence', 0),
                    'rag': result.get('metadata', {}).get('rag_confidence', 0),
                    'overall': (result.get('metadata', {}).get('extraction_confidence', 0) + 
                              result.get('metadata', {}).get('rag_confidence', 0)) / 2
                }
            },
            'sources': [{
                'section': src.get('section', ''),
                'content_preview': src.get('content', '')[:150] + '...',
                'relevance_score': src.get('relevanceScore', 0),
                'file_name': src.get('file_name', '')
            } for src in result.get('search_results', [])],
            'user_preferences': user_preferences
        })
        
    except Exception as e:
        print(f"Error in multi-LLM search: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500
# Global instances
conversation_manager = ConversationManager()
enhanced_processor = None

# Initialize enhanced processor
try:
    enhanced_processor = EnhancedLegalDocumentProcessor()
    print("Enhanced Legal Document Processor with thread management created successfully")
except Exception as e:
    print(f"Error creating enhanced processor: {e}")

# Global processor instance
print("🔄 Initializing Legal Document Processor...")
try:
    # You can add your OpenAI API key here if you have one
    # processor = LegalDocumentProcessor(openai_api_key="your-api-key-here")
    processor = LegalDocumentProcessor()
    print("✅ Legal Document Processor created successfully")
except Exception as e:
    print(f"❌ Error creating processor: {e}")
    processor = None

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS


@app.route('/api/upload', methods=['POST'])
def upload_documents():
    """Upload and process documents with enhanced error handling"""
    try:
        if not processor:
            return jsonify({'error': 'Document processor not initialized'}), 500
        
        if 'files' not in request.files:
            return jsonify({'error': 'No files provided'}), 400
        
        files = request.files.getlist('files')
        if not files or all(f.filename == '' for f in files):
            return jsonify({'error': 'No files selected'}), 400
        
        # Get user labels from form data
        user_labels = {}
        if 'labels' in request.form:
            try:
                user_labels = json.loads(request.form['labels'])
            except json.JSONDecodeError:
                user_labels = {}
        
        results = []
        
        for file in files:
            if file and file.filename:
                try:
                    filename = secure_filename(file.filename)
                    file_content = file.read()
                    
                    # Process document with metadata
                    result = processor.process_document_with_metadata(
                        file_content, filename, user_labels
                    )
                    
                    results.append({
                        'filename': filename,
                        'status': 'success',
                        'message': f'Successfully processed {filename}',
                        'chunks_created': result.get('chunks_created', 0)
                    })
                    
                except Exception as e:
                    print(f"❌ Upload error for {file.filename}: {str(e)}")
                    results.append({
                        'filename': file.filename,
                        'status': 'error',
                        'message': str(e)
                    })
        
        success_count = sum(1 for r in results if r['status'] == 'success')
        
        return jsonify({
            'status': 'completed' if success_count > 0 else 'failed',
            'results': results,
            'total_files': len(files),
            'successful_uploads': success_count
        })
        
    except Exception as e:
        print(f"❌ General upload error: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/query', methods=['POST'])
def query_documents():
    """Query documents using multi-LLM architecture"""
    try:
        if not processor:
            return jsonify({'error': 'Document processor not initialized'}), 500
        
        data = request.get_json()
        if not data or 'query' not in data:
            return jsonify({'error': 'Query is required'}), 400
        
        query = data['query']
        top_k = data.get('top_k', 5)
        print("top_k----",top_k)
        user_preferences = data.get('user_preferences', {})
        
        # Use multi-LLM search and answer
        result = processor.multi_llm_search_and_answer(
            query, top_k, user_preferences
        )
        
        return jsonify(result)
        
    except Exception as e:
        print(f"❌ Query error: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/api/documents/metadata', methods=['GET'])
def get_documents_metadata():
    """Get metadata for all processed documents"""
    try:
        if not processor:
            return jsonify({'error': 'Document processor not initialized'}), 500
        
        metadata = processor.get_document_metadata()
        
        return jsonify({
            'status': 'success',
            'documents': metadata,
            'total_count': len(metadata)
        })
        
    except Exception as e:
        print(f"❌ Metadata retrieval error: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/api/documents/metadata/<filename>', methods=['GET'])
def get_document_metadata(filename):
    """Get metadata for a specific document"""
    try:
        if not processor:
            return jsonify({'error': 'Document processor not initialized'}), 500
        
        metadata = processor.get_document_metadata(filename)
        
        if not metadata:
            return jsonify({'error': 'Document not found'}), 404
        
        return jsonify({
            'status': 'success',
            'document': filename,
            'metadata': metadata
        })
        
    except Exception as e:
        print(f"❌ Metadata retrieval error: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500
@app.route('/api/process', methods=['POST'])
def process_documents():
    """Process uploaded documents"""
    if not processor:
        return jsonify({
            'status': 'error',
            'message': 'Processor not available. Please restart the server.'
        }), 500
        
    if not processor.is_initialized():
        return jsonify({
            'status': 'error',
            'message': 'Processor not initialized. Please check server logs.'
        }), 500
    
    # Get all supported files from upload folder
    upload_path = Path(app.config['UPLOAD_FOLDER'])
    supported_files = []
    for ext in ALLOWED_EXTENSIONS:
        supported_files.extend(upload_path.glob(f'*.{ext}'))

    if not supported_files:
        return jsonify({
            'status': 'error',
            'message': 'No supported files found to process'
        }), 400
    
    print(f"Processing {len(supported_files)} documents...")

    results = []
    total_chunks = 0

    for file_path in supported_files:
        print(f"📄 Processing: {file_path.name}")
        result = processor.process_legal_document(str(file_path))
        
        results.append({
            'file': file_path.name,
            'status': result['status'],
            'chunks': result['chunks'],
            'message': result['message']
        })
        
        if result['status'] == 'success':
            total_chunks += result['chunks']
    
    return jsonify({
        'status': 'success',
        'message': 'Processing complete',
        'results': results,
        'total_chunks': total_chunks
    })
def generate_response(query, results):
    """Generate a refined, fine-tuned chatbot response based on search results"""
    if not results:
        return f"I couldn't find any relevant information in the processed documents for your query: '{query}'. Try rephrasing your question or using different keywords."
    
    response_parts = []
    response_parts.append("Chatbot Answer: \n")
    
    # Iterate over the search results and generate the responses
    for i, result in enumerate(results[:2], 1):  # Limit to the top 2 most relevant results
        section = result.get('section', 'Unknown Section')
        subsection = result.get('subsection', '')
        content = result.get('content', '')
        relevance_score = result.get('relevanceScore', 0)
        
        # Format the answer with the section and subsection
        answer = "**First answer**\n\n"
        answer += f"{section} {subsection}\n\n"
        answer += f"{content[:300]}..."  # Excerpt from the content for clarity
        answer += f"\n\n**Relevance**: {relevance_score * 100:.1f}%"
        
        response_parts.append(f"{answer}")
    
    # Adding additional answers if available
    for i, result in enumerate(results[2:4], 1):  # Additional answers for more context
        section = result.get('section', 'Unknown Section')
        subsection = result.get('subsection', '')
        content = result.get('content', '')
        relevance_score = result.get('relevanceScore', 0)
        
        answer = "**Second answer**\n\n"
        answer += f"{section} {subsection}\n\n"
        answer += f"{content[:300]}..."  # Excerpt from the content
        answer += f"\n\n**Relevance**: {relevance_score * 100:.1f}%"
        
        response_parts.append(f"{answer}")
    
    return "\n".join(response_parts)



def format_sources(results):
    """Format sources with better structure for the chatbot response"""
    formatted_sources = []
    
    for result in results:
        formatted_source = {
            'section': result.get('section', ''),
            'subsection': result.get('subsection', ''),
            'content': result['content'],
            'relevanceScore': round(result.get('relevanceScore', 0), 3),
            'file_name': result.get('file_name', ''),
            'excerpt': result.get('content', '')[:200] + "...",  # Short preview of content
            'metadata': {
                'content_length': len(result['content']),
                'has_procedures': bool(re.search(r'\([0-9]+\)|[0-9]+\.', result['content'])),
                'has_requirements': bool(re.search(r'shall|must|required', result['content'], re.IGNORECASE)),
                'has_timelines': bool(re.search(r'\d+\s+days?|\d+\s+months?|within', result['content'], re.IGNORECASE))
            }
        }
        formatted_sources.append(formatted_source)
    
    return formatted_sources

def generate_specific_legal_response(query, results):
    """Generate specific, contextual responses based on search results and query type"""
    
    # Filter results based on query relevance and include chunk content
    response = f"Answer to your query '{query}':\n\n"
    for i, result in enumerate(results[:3], 1):  # Limit to top 3 most relevant results
        section = result.get('section', 'Unknown Section')
        subsection = result.get('subsection', '')
        content = result.get('content', '')
        relevance_score = result.get('relevanceScore', 0)
        
        # Display the section and content in the answer
        response += f"**Answer {i}:**\n"
        response += f"**Section:** {section} {subsection}\n"
        response += f"**Content:** {content[:300]}...\n"  # Show excerpt from content
        response += f"**Relevance Score:** {relevance_score * 100:.1f}%\n\n"
    
    return response.strip()


def classify_query_type(query_lower):
    """Classify the type of legal query"""
    
    warranty_keywords = ['warranty', 'claim', 'defect', 'repair', 'coverage', 'reimbursement']
    procedure_keywords = ['how to', 'process', 'submit', 'file', 'steps', 'procedure']
    definition_keywords = ['what is', 'define', 'definition', 'meaning', 'means']
    requirement_keywords = ['required', 'must', 'shall', 'obligation', 'requirement']
    timeline_keywords = ['when', 'deadline', 'time', 'days', 'period', 'duration']
    
    if any(keyword in query_lower for keyword in warranty_keywords):
        return 'warranty_claim'
    elif any(keyword in query_lower for keyword in procedure_keywords):
        return 'procedure'
    elif any(keyword in query_lower for keyword in definition_keywords):
        return 'definition'
    elif any(keyword in query_lower for keyword in requirement_keywords):
        return 'requirement'
    elif any(keyword in query_lower for keyword in timeline_keywords):
        return 'timeline'
    else:
        return 'general'
def extract_specific_answer(content, query):
        """Extract a specific, concise answer from content based on query"""
        query_lower = query.lower()
        
        # Look for specific patterns based on common legal questions
        if 'notice' in query_lower and ('days' in query_lower or 'time' in query_lower):
            # Look for time periods with notice requirements
            notice_patterns = [
                r'(\d+)\s+days?\s+(?:written\s+)?notice',
                r'(?:written\s+)?notice\s+(?:of\s+)?(\d+)\s+days?',
                r'(\d+)\s+days?\s+(?:advance\s+)?(?:written\s+)?notice',
                r'notice\s+(?:period\s+)?(?:of\s+)?(\d+)\s+days?'
            ]
            
            for pattern in notice_patterns:
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    days = match.group(1)
                    return f"The motor vehicle dealer must give the licensee {days} days' written notice before requesting a warranty parts reimbursement increase."
        
        elif 'warranty' in query_lower and 'reimbursement' in query_lower:
            # Look for warranty reimbursement information
            warranty_sentences = []
            sentences = content.split('.')
            for sentence in sentences:
                if 'warranty' in sentence.lower() and 'reimbursement' in sentence.lower():
                    warranty_sentences.append(sentence.strip())
            
            if warranty_sentences:
                return warranty_sentences[0] + '.'
        
        elif 'deadline' in query_lower or 'timeline' in query_lower:
            # Look for deadline information
            deadline_patterns = [
                r'(?:within\s+)?(\d+)\s+days?(?:\s+of|\s+from|\s+after)',
                r'(\d+)\s+days?\s+(?:deadline|timeline|period)',
                r'(?:no\s+later\s+than\s+)?(\d+)\s+days?'
            ]
            
            for pattern in deadline_patterns:
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    days = match.group(1)
                    return f"The deadline is {days} days from the specified trigger date."
        
        elif 'required' in query_lower or 'must' in query_lower:
            # Look for requirement statements
            requirement_patterns = [
                r'([^.]*\b(?:shall|must|required)\b[^.]*\.)',
                r'([^.]*\b(?:obligation|duty)\b[^.]*\.)'
            ]
            
            for pattern in requirement_patterns:
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    return match.group(1).strip()
        
        # Default: extract most relevant sentence
        sentences = content.split('.')
        query_terms = query_lower.split()
        
        best_sentence = ""
        best_score = 0
        
        for sentence in sentences:
            sentence_lower = sentence.lower()
            score = sum(1 for term in query_terms if term in sentence_lower)
            
            if score > best_score and len(sentence.strip()) > 20:
                best_score = score
                best_sentence = sentence.strip()
        
        return best_sentence + '.' if best_sentence else "Information not found in the document."
def generate_concise_legal_response(query, results):
    """Generate a single, concise answer in Q&A format like ChatGPT"""
    if not results:
        return f"**Q:** {query}\n\n**A:** I couldn't find specific information about this question in the processed legal documents."

    # Get the most relevant result
    best_result = results[0]
    content = best_result['content']

    # Extract specific answer
    specific_answer = extract_specific_answer(content, query)

    if specific_answer and specific_answer != "Information not found in the document.":
        return f"**Q:** {query}\n\n**A:** {specific_answer}"

    # Fallback: create answer from section and content
    section = best_result.get('section', '')
    subsection = best_result.get('subsection', '')

    # Try to find a direct answer in the content
    sentences = content.split('.')
    for sentence in sentences:
        if len(sentence.strip()) > 30 and any(term in sentence.lower() for term in query.lower().split()):
            clean_sentence = sentence.strip()
            if not clean_sentence.endswith('.'):
                clean_sentence += '.'
            return f"**Q:** {query}\n\n**A:** {clean_sentence}"

    # Final fallback
    return f"**Q:** {query}\n\n**A:** Based on {section}, {content[:150]}..."

def generate_warranty_response(query, results):
    """Generate specific warranty-related response in Q&A format"""
    if not results:
        return f"**Q:** {query}\n\n**A:** No warranty information found for this question."

    # Get the best result
    best_result = results[0]
    content = best_result['content']

    # Extract specific warranty answer
    specific_answer = extract_specific_answer(content, query)

    if specific_answer and "not found" not in specific_answer.lower():
        return f"**Q:** {query}\n\n**A:** {specific_answer}"

    # Fallback to first relevant warranty sentence
    sentences = content.split('.')
    for sentence in sentences:
        if ('warranty' in sentence.lower() and
            any(term in sentence.lower() for term in ['notice', 'days', 'reimbursement', 'increase'])):
            return f"**Q:** {query}\n\n**A:** {sentence.strip()}."

    return f"**Q:** {query}\n\n**A:** Warranty information is covered under {best_result.get('section', 'the relevant section')} but specific details need manual review."

def generate_procedure_response(query, results):
    """Generate step-by-step procedure response - MODIFIED for conciseness"""
    if not results:
        return f"No procedure information found for '{query}'."
    
    best_result = results[0]
    content = best_result['content']
    
    # Look for direct procedural answer
    specific_answer = extract_specific_answer(content, query)
    
    if specific_answer and "not found" not in specific_answer.lower():
        return f"**A:** {specific_answer}"
    
    # Extract first clear procedural step
    step_patterns = [
        r'(\([0-9]+\)[^.]*\.)',  # (1) step
        r'(\d+\.\s[^.]*\.)',     # 1. step
    ]
    
    for pattern in step_patterns:
        match = re.search(pattern, content)
        if match:
            return f"**A:** {match.group(1).strip()}"
    
    return f"**A:** The procedure is outlined in {best_result.get('section', 'the relevant section')} of the document."

def generate_definition_response(query, results):
    """Generate definition-focused response"""
    response = f"Definition and explanation for '{query}':\n\n"
    
    for i, result in enumerate(results[:2], 1):
        section = result.get('section', 'Unknown Section')
        content = result['content']
        
        # Extract definitions and explanations
        definition = extract_definition(content, query)
        
        response += f"**{i}. {section}**\n"
        response += f"{definition}\n\n"
    
    return response.strip()

def generate_requirement_response(query, results):
    """Generate requirements-focused response"""
    response = f"Requirements related to '{query}':\n\n"
    
    for i, result in enumerate(results[:3], 1):
        section = result.get('section', 'Unknown Section')
        content = result['content']
        
        # Extract requirements
        requirements = extract_requirements(content)
        
        response += f"**{i}. {section}**\n"
        for req in requirements:
            response += f"• {req}\n"
        response += "\n"
    
    return response.strip()

def generate_timeline_response(query, results):
    """Generate timeline-focused response"""
    response = f"Timeline information for '{query}':\n\n"
    
    for i, result in enumerate(results[:3], 1):
        section = result.get('section', 'Unknown Section')
        content = result['content']
        
        # Extract timeline information
        timelines = extract_timeline_info(content)
        
        response += f"**{i}. {section}**\n"
        for timeline in timelines:
            response += f"• {timeline}\n"
        response += "\n"
    
    return response.strip()

def generate_general_specific_response(query, results):
    """Generate specific response for general queries"""
    response = f"Specific information found for '{query}':\n\n"
    
    for i, result in enumerate(results[:3], 1):
        section = result.get('section', 'Unknown Section')
        subsection = result.get('subsection', '')
        content = result['content']
        relevance = result.get('relevanceScore', 0)
        
        # Extract most relevant sentence/paragraph
        relevant_excerpt = extract_most_relevant_excerpt(content, query)
        
        response += f"**{i}. {section}**"
        if subsection:
            response += f" - {subsection}"
        response += f" (Relevance: {relevance:.2f})\n\n"
        response += f"{relevant_excerpt}\n\n"
    
    return response.strip()

def extract_warranty_details(content):
    """Extract specific warranty-related details from content"""
    details = {
        'reimbursement': '',
        'timeline': '',
        'requirements': '',
        'procedure': ''
    }
    
    content_lower = content.lower()
    
    # Extract reimbursement information
    if 'reimbursement' in content_lower or 'compensation' in content_lower:
        reimbursement_match = re.search(r'([^.]*(?:reimbursement|compensation)[^.]*\.)', content, re.IGNORECASE)
        if reimbursement_match:
            details['reimbursement'] = reimbursement_match.group(1).strip()
    
    # Extract timeline information
    timeline_patterns = [
        r'(\d+\s+days?)',
        r'(within\s+\d+\s+days?)',
        r'(\d+\s+months?)',
        r'(within\s+\d+\s+months?)'
    ]
    
    for pattern in timeline_patterns:
        timeline_match = re.search(pattern, content, re.IGNORECASE)
        if timeline_match:
            details['timeline'] = timeline_match.group(1)
            break
    
    # Extract requirements
    if 'shall' in content_lower or 'must' in content_lower or 'required' in content_lower:
        req_match = re.search(r'([^.]*(?:shall|must|required)[^.]*\.)', content, re.IGNORECASE)
        if req_match:
            details['requirements'] = req_match.group(1).strip()
    
    return details

def extract_procedure_steps(content):
    """Extract procedural steps from content"""
    steps = []
    
    # Look for numbered or lettered steps
    step_patterns = [
        r'(\([0-9]+\)[^.]*\.)',  # (1) step
        r'(\([a-z]\)[^.]*\.)',   # (a) step
        r'(\d+\.\s[^.]*\.)',     # 1. step
        r'([a-z]\.\s[^.]*\.)'    # a. step
    ]
    
    for pattern in step_patterns:
        matches = re.findall(pattern, content, re.IGNORECASE)
        if matches:
            steps.extend([match.strip() for match in matches])
            break
    
    return steps[:5]  # Limit to first 5 steps

def extract_definition(content, query):
    """Extract definition or explanation from content"""
    # Look for sentences containing the query term
    sentences = content.split('.')
    relevant_sentences = []
    
    query_terms = query.lower().split()
    
    for sentence in sentences:
        sentence_lower = sentence.lower()
        if any(term in sentence_lower for term in query_terms):
            relevant_sentences.append(sentence.strip())
    
    return '. '.join(relevant_sentences[:2]) + '.' if relevant_sentences else content[:200] + '...'

def extract_requirements(content):
    """Extract requirements from content"""
    requirements = []
    
    # Split into sentences and find those with requirement keywords
    sentences = content.split('.')
    requirement_keywords = ['shall', 'must', 'required', 'obligation', 'duty']
    
    for sentence in sentences:
        sentence_lower = sentence.lower()
        if any(keyword in sentence_lower for keyword in requirement_keywords):
            requirements.append(sentence.strip())
    
    return requirements[:3]  # Limit to top 3

def extract_timeline_info(content):
    """Extract timeline information from content"""
    timelines = []
    
    # Look for time-related patterns
    time_patterns = [
        r'([^.]*\d+\s+days?[^.]*\.)',
        r'([^.]*\d+\s+months?[^.]*\.)',
        r'([^.]*within[^.]*\.)',
        r'([^.]*deadline[^.]*\.)',
        r'([^.]*period[^.]*\.)'
    ]
    
    for pattern in time_patterns:
        matches = re.findall(pattern, content, re.IGNORECASE)
        timelines.extend([match.strip() for match in matches])
    
    return timelines[:3]  # Limit to top 3

def extract_most_relevant_excerpt(content, query):
    """Extract the most relevant excerpt based on query"""
    sentences = content.split('.')
    query_terms = query.lower().split()
    
    # Score sentences based on query term matches
    scored_sentences = []
    for sentence in sentences:
        sentence_lower = sentence.lower()
        score = sum(1 for term in query_terms if term in sentence_lower)
        if score > 0:
            scored_sentences.append((score, sentence.strip()))
    
    # Return the highest scoring sentences
    if scored_sentences:
        scored_sentences.sort(key=lambda x: x[0], reverse=True)
        return '. '.join([sent[1] for sent in scored_sentences[:2]]) + '.'
    
    return content[:300] + '...'

def format_sources(results):
    """Format sources with better structure"""
    formatted_sources = []
    
    for result in results:
        formatted_source = {
            'section': result.get('section', ''),
            'subsection': result.get('subsection', ''),
            'content': result['content'],
            'relevanceScore': round(result.get('relevanceScore', 0), 3),
            'file_name': result.get('file_name', ''),
            'excerpt': extract_most_relevant_excerpt(result['content'], ""),
            'metadata': {
                'content_length': len(result['content']),
                'has_procedures': bool(re.search(r'\([0-9]+\)|[0-9]+\.', result['content'])),
                'has_requirements': bool(re.search(r'shall|must|required', result['content'], re.IGNORECASE)),
                'has_timelines': bool(re.search(r'\d+\s+days?|\d+\s+months?|within', result['content'], re.IGNORECASE))
            }
        }
        formatted_sources.append(formatted_source)
    
    return formatted_sources


# Also update the advanced search function
@app.route('/api/search/advanced', methods=['POST'])
def advanced_search():
    """Advanced search with enhanced specific formatting"""
    data = request.get_json()
    query = data.get('query', '')
    top_k = data.get('top_k', 5)
    search_type = data.get('search_type', 'hybrid')
    file_filter = data.get('file_filter', None)
    section_filter = data.get('section_filter', None)
    response_format = data.get('response_format', 'detailed')  # detailed, summary, bullets
    
    if not query:
        return jsonify({
            'status': 'error',
            'message': 'No query provided'
        }), 400
    
    if not processor or not processor.chunks:
        return jsonify({
            'status': 'error',
            'message': 'No documents processed yet.'
        }), 400
    
    try:
        # Filter chunks if needed
        filtered_chunks = processor.chunks
        
        if file_filter:
            filtered_chunks = [c for c in filtered_chunks if c.get('file_name', '').lower().find(file_filter.lower()) >= 0]
        
        if section_filter:
            filtered_chunks = [c for c in filtered_chunks if c.get('section', '').lower().find(section_filter.lower()) >= 0]
        
        if not filtered_chunks:
            return jsonify({
                'status': 'success',
                'response': 'No information found matching the specified filters.',
                'sources': []
            })
        
        # Temporarily replace processor chunks for filtered search
        original_chunks = processor.chunks
        processor.chunks = filtered_chunks
        
        # Perform search
        if search_type == 'hybrid':
            results = processor.hybrid_search(query, top_k)
        elif search_type == 'keyword':
            results = perform_keyword_search(query, filtered_chunks, top_k)
        else:
            results = processor.hybrid_search(query, top_k)
        
        # Restore original chunks
        processor.chunks = original_chunks
        
        if not results:
            return jsonify({
                'status': 'success',
                'response': f'No specific information found for "{query}" with the applied filters.',
                'sources': []
            })
        
        # Generate response based on format preference
        if response_format == 'summary':
            response = generate_summary_response(query, results)
        elif response_format == 'bullets':
            response = generate_bullet_response(query, results)
        else:
            response = generate_specific_legal_response(query, results)
        
        return jsonify({
            'status': 'success',
            'response': response,
            'search_metadata': {
                'search_type': search_type,
                'response_format': response_format,
                'total_chunks_searched': len(filtered_chunks),
                'results_returned': len(results),
                'filters_applied': {
                    'file_filter': file_filter,
                    'section_filter': section_filter
                }
            },
            'sources': format_sources(results)
        })
        
    except Exception as e:
        print(f"Error in advanced search: {e}")
        traceback.print_exc()
        return jsonify({
            'status': 'error',
            'message': f'Advanced search failed: {str(e)}'
        }), 500

def generate_summary_response(query, results):
    """Generate a concise summary response"""
    if not results:
        return f"No information found for '{query}'."
    
    key_points = []
    for result in results[:2]:  # Top 2 results only
        excerpt = extract_most_relevant_excerpt(result['content'], query)
        key_points.append(excerpt)
    
    return f"**Summary for '{query}':**\n\n" + "\n\n".join(key_points)

def generate_bullet_response(query, results):
    """Generate bullet-point response"""
    response = f"**Key points for '{query}':**\n\n"
    
    for i, result in enumerate(results[:4], 1):
        section = result.get('section', 'Unknown Section')
        excerpt = extract_most_relevant_excerpt(result['content'], query)
        
        # Limit excerpt length for bullet format
        if len(excerpt) > 150:
            excerpt = excerpt[:150] + "..."
        
        response += f"• **{section}**: {excerpt}\n\n"
    
    return response.strip()

def perform_keyword_search(query, chunks, top_k):
    """Perform simple keyword search with scoring"""
    query_lower = query.lower()
    keyword_results = []
    
    for chunk in chunks:
        content_lower = chunk['content'].lower()
        if query_lower in content_lower:
            # Calculate score based on frequency and position
            count = content_lower.count(query_lower)
            position_score = 1.0 if content_lower.find(query_lower) < 200 else 0.5
            score = (count / len(content_lower.split())) * position_score
            
            result_chunk = chunk.copy()
            result_chunk['relevanceScore'] = min(score * 10, 1.0)
            keyword_results.append(result_chunk)
    
    keyword_results.sort(key=lambda x: x['relevanceScore'], reverse=True)
    return keyword_results[:top_k]

@app.route('/api/clear', methods=['POST'])
def clear_data():
    """Clear all processed data"""
    if processor:
        processor.clear_data()
    
    # Also clear uploaded files
    upload_path = Path(app.config['UPLOAD_FOLDER'])
    for file_path in upload_path.iterdir():
        if file_path.is_file():
            file_path.unlink()
    
    return jsonify({
        'status': 'success',
        'message': 'All data cleared successfully'
    })

@app.route('/api/settings', methods=['GET'])
def get_settings():
    """Get current system settings"""
    if not processor:
        return jsonify({
            'status': 'error',
            'message': 'Processor not available'
        }), 500
    
    return jsonify({
        'status': 'success',
        'config': processor.get_config()
    })

# Modified settings update to handle API keys
@app.route('/api/settings', methods=['POST'])
def update_settings():
    """Update system settings including API keys"""
    if not processor:
        return jsonify({
            'status': 'error',
            'message': 'Processor not available'
        }), 500
    
    data = request.get_json()
    
    try:
        # Handle API key updates
        if 'openai_api_key' in data:
            ai_client.initialize_openai(data['openai_api_key'])
            # Don't store the actual key in processor config
            data.pop('openai_api_key')
        
        if 'cohere_api_key' in data:
            ai_client.set_cohere_key(data['cohere_api_key'])
            data.pop('cohere_api_key')
        
        if 'huggingface_api_key' in data:
            ai_client.set_huggingface_key(data['huggingface_api_key'])
            data.pop('huggingface_api_key')
        
        # Update processor settings
        processor.update_config(**data)
        
        return jsonify({
            'status': 'success',
            'message': 'Settings updated successfully',
            'config': processor.get_config()
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'Failed to update settings: {str(e)}'
        }), 500

# Additional utility endpoints
@app.route('/api/chunks', methods=['GET'])
def get_chunks():
    """Get information about processed chunks"""
    if not processor:
        return jsonify({
            'status': 'error',
            'message': 'Processor not available'
        }), 500
    
    chunks_info = []
    for chunk in processor.chunks[:10]:  # Limit to first 10 for preview
        chunks_info.append({
            'id': chunk.get('id', 'unknown'),
            'section': chunk.get('section', ''),
            'subsection': chunk.get('subsection', ''),
            'content_preview': chunk.get('content', '')[:200] + '...',
            'file_name': chunk.get('file_name', ''),
            'has_embedding': chunk.get('dense_embedding') is not None
        })
    
    return jsonify({
        'status': 'success',
        'total_chunks': len(processor.chunks),
        'chunks_preview': chunks_info,
        'processed_files': processor.processed_files
    })

# Add new endpoint to test AI model connectivity
@app.route('/api/test-model', methods=['POST'])
def test_ai_model():
    """Test AI model connectivity"""
    data = request.get_json()
    provider = data.get('provider', 'ollama')
    model_name = data.get('model_name', 'llama3.2')
    
    try:
        test_response = ai_client.generate_response(
            prompt="You are a helpful assistant. Respond with 'Test successful' if you receive this message.",
            context="This is a test message.",
            provider=provider,
            model_name=model_name
        )
        
        if test_response:
            return jsonify({
                'status': 'success',
                'message': f'Model {model_name} on {provider} is working correctly',
                'test_response': test_response
            })
        else:
            return jsonify({
                'status': 'error',
                'message': f'Failed to get response from {model_name} on {provider}'
            })
            
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'Model test failed: {str(e)}'
        }), 500

if __name__ == '__main__':
    print("🚀 Starting Enhanced Legal Document Chatbot Server...")
    print(f"   Processor initialized: {processor.is_initialized() if processor else False}")
    print(f"   Docling available: {DOCLING_AVAILABLE}")
    print(f"   scikit-learn available: {SKLEARN_AVAILABLE}")
    print(f"   BM25 available: {BM25_AVAILABLE}")
    print(f"   OpenAI available: {OPENAI_AVAILABLE}")
    print("   Available search methods: Hybrid, Sparse (BM25/TF-IDF), Dense (OpenAI), Keyword")
    print("   Server will be available at: http://localhost:5000")
    print("\n📋 Available endpoints:")
    print("   - GET  /api/health - Server health check")
    print("   - POST /api/upload - Upload documents")
    print("   - POST /api/process - Process uploaded documents")
    print("   - POST /api/search - Basic search")
    print("   - POST /api/search/advanced - Advanced search with filters")
    print("   - GET  /api/chunks - View processed chunks")
    print("   - POST /api/clear - Clear all data")
    print("   - GET  /api/settings - View settings")
    print("   - POST /api/settings - Update settings")
    
    app.run(debug=True, host='0.0.0.0', port=5000)