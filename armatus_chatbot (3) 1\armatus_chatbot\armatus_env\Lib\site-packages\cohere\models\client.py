# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ..core.client_wrapper import Async<PERSON><PERSON><PERSON>rapper, Sync<PERSON>lientWrapper
from ..core.request_options import RequestOptions
from ..types.compatible_endpoint import CompatibleEndpoint
from ..types.get_model_response import GetModelResponse
from ..types.list_models_response import ListModelsResponse
from .raw_client import AsyncRawModelsClient, RawModelsClient


class ModelsClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawModelsClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawModelsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawModelsClient
        """
        return self._raw_client

    def get(self, model: str, *, request_options: typing.Optional[RequestOptions] = None) -> GetModelResponse:
        """
        Returns the details of a model, provided its name.

        Parameters
        ----------
        model : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetModelResponse
            OK

        Examples
        --------
        from cohere import Client

        client = Client(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )
        client.models.get(
            model="command-a-03-2025",
        )
        """
        _response = self._raw_client.get(model, request_options=request_options)
        return _response.data

    def list(
        self,
        *,
        page_size: typing.Optional[float] = None,
        page_token: typing.Optional[str] = None,
        endpoint: typing.Optional[CompatibleEndpoint] = None,
        default_only: typing.Optional[bool] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> ListModelsResponse:
        """
        Returns a list of models available for use. The list contains models from Cohere as well as your fine-tuned models.

        Parameters
        ----------
        page_size : typing.Optional[float]
            Maximum number of models to include in a page
            Defaults to `20`, min value of `1`, max value of `1000`.

        page_token : typing.Optional[str]
            Page token provided in the `next_page_token` field of a previous response.

        endpoint : typing.Optional[CompatibleEndpoint]
            When provided, filters the list of models to only those that are compatible with the specified endpoint.

        default_only : typing.Optional[bool]
            When provided, filters the list of models to only the default model to the endpoint. This parameter is only valid when `endpoint` is provided.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ListModelsResponse
            OK

        Examples
        --------
        from cohere import Client

        client = Client(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )
        client.models.list()
        """
        _response = self._raw_client.list(
            page_size=page_size,
            page_token=page_token,
            endpoint=endpoint,
            default_only=default_only,
            request_options=request_options,
        )
        return _response.data


class AsyncModelsClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawModelsClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawModelsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawModelsClient
        """
        return self._raw_client

    async def get(self, model: str, *, request_options: typing.Optional[RequestOptions] = None) -> GetModelResponse:
        """
        Returns the details of a model, provided its name.

        Parameters
        ----------
        model : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetModelResponse
            OK

        Examples
        --------
        import asyncio

        from cohere import AsyncClient

        client = AsyncClient(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.models.get(
                model="command-a-03-2025",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.get(model, request_options=request_options)
        return _response.data

    async def list(
        self,
        *,
        page_size: typing.Optional[float] = None,
        page_token: typing.Optional[str] = None,
        endpoint: typing.Optional[CompatibleEndpoint] = None,
        default_only: typing.Optional[bool] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> ListModelsResponse:
        """
        Returns a list of models available for use. The list contains models from Cohere as well as your fine-tuned models.

        Parameters
        ----------
        page_size : typing.Optional[float]
            Maximum number of models to include in a page
            Defaults to `20`, min value of `1`, max value of `1000`.

        page_token : typing.Optional[str]
            Page token provided in the `next_page_token` field of a previous response.

        endpoint : typing.Optional[CompatibleEndpoint]
            When provided, filters the list of models to only those that are compatible with the specified endpoint.

        default_only : typing.Optional[bool]
            When provided, filters the list of models to only the default model to the endpoint. This parameter is only valid when `endpoint` is provided.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ListModelsResponse
            OK

        Examples
        --------
        import asyncio

        from cohere import AsyncClient

        client = AsyncClient(
            client_name="YOUR_CLIENT_NAME",
            token="YOUR_TOKEN",
        )


        async def main() -> None:
            await client.models.list()


        asyncio.run(main())
        """
        _response = await self._raw_client.list(
            page_size=page_size,
            page_token=page_token,
            endpoint=endpoint,
            default_only=default_only,
            request_options=request_options,
        )
        return _response.data
